# ChatBI系统架构

## 当前架构（Flask）

### 技术选型理由
- **Flask**: 轻量级、灵活，适合快速原型开发
- **LangChain**: 提供强大的LLM链式处理能力
- **SQLAlchemy**: 成熟的ORM，支持连接池和查询优化
- **Server-Sent Events**: 实现流式响应，用户体验良好

### 架构层次
```
┌─────────────────┐
│   前端客户端     │
└─────────┬───────┘
          │ HTTP/SSE
┌─────────▼───────┐
│   Flask API     │
│   - routes.py   │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   服务层        │
│   - query_service│
│   - chains      │
│   - llm_clients │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   数据层        │
│   - database    │
│   - schema_mgr  │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   MySQL数据库   │
└─────────────────┘
```

### 关键设计决策
1. **异步处理**: 使用线程+队列桥接Flask同步和异步LangChain
2. **流式响应**: 通过SSE实现实时数据流传输
3. **连接池**: SQLAlchemy引擎管理数据库连接
4. **模块化**: 服务层高度模块化，便于维护

## 目标架构（FastAPI）

### 迁移后的技术栈
- **FastAPI**: 现代异步Web框架，原生支持异步处理
- **Uvicorn**: ASGI服务器，高性能异步处理
- **保持现有**: LangChain、SQLAlchemy、MySQL配置

### 预期改进
1. **性能提升**: 原生异步支持，消除线程开销
2. **代码简化**: 移除复杂的同步/异步桥接逻辑
3. **自动文档**: FastAPI自动生成OpenAPI文档
4. **类型安全**: Pydantic模型提供更好的类型检查

### 迁移约束
- **接口兼容**: 所有API端点保持完全兼容
- **数据库不变**: 保持现有数据库配置和连接方式
- **业务逻辑不变**: 所有查询处理逻辑保持一致
- **环境变量不变**: 保持现有配置文件格式
