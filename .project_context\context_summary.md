# ChatBI项目上下文摘要

## 项目概述
ChatBI是一个基于Flask的智能数据分析平台，通过自然语言查询生成SQL并提供流式数据分析服务。项目集成了LangChain、OpenAI API和MySQL数据库，支持复杂的专家信息查询和位运算字段处理。

## 技术架构

### 当前技术栈
- **Web框架**: Flask 3.1.1 + Flask-CORS
- **异步处理**: asyncio + threading + queue (混合模式)
- **数据库**: MySQL + SQLAlchemy 2.0.41 + PyMySQL
- **AI服务**: LangChain + OpenAI兼容API (阿里云百炼)
- **流式响应**: Server-Sent Events (SSE)
- **部署**: Werkzeug开发服务器

### 项目结构
```
ChatBI/
├── app/
│   ├── __init__.py          # Flask应用工厂
│   ├── routes.py            # API路由定义
│   └── services/            # 业务逻辑层
│       ├── chains.py        # LangChain LCEL链
│       ├── database.py      # 数据库连接配置
│       ├── llm_clients.py   # LLM客户端配置
│       ├── prompts.py       # AI提示模板
│       ├── query_service.py # 核心查询服务
│       └── schema_manager.py # 数据库模式管理
├── run.py                   # 应用启动入口
├── requirements.txt         # 依赖包列表
└── .env                     # 环境变量配置
```

## 核心功能

### API端点
1. **POST /api/suggestions** - 输入联想建议
2. **POST /api/query** - 自然语言查询（流式响应）

### 查询处理流程
1. **阶段一**: 并行启动计划文本流和后台数据获取
2. **阶段二**: 等待数据任务完成，发送SQL和原始数据
3. **阶段三**: 基于真实数据生成最终分析总结

### 特殊功能
- 位运算字段自动解码（officer、title等字段）
- 多表关联查询支持
- SQL语法验证和优化
- 异步流式响应处理

## 数据库配置
- **连接**: MySQL通过SQLAlchemy引擎
- **主表**: erp_expert (专家信息)
- **关联表**: 9个相关表（apply、dev、favorite等）
- **特殊字段**: 位运算字段需要特殊解码处理

## 环境配置
- **数据库**: `DB_URL` - MySQL连接字符串
- **AI服务**: `OPENAI_API_BASE`、`OPENAI_API_KEY`、`OPENAI_MODEL_NAME`
- **当前配置**: 阿里云百炼 qwen-turbo 模型

## 已知技术债务
1. **混合同步/异步架构**: Flask同步框架中嵌入异步处理，通过线程+队列桥接
2. **重复的SQL执行函数**: routes.py和query_service.py中都有SQL执行逻辑
3. **硬编码配置**: 部分配置直接写在代码中
4. **测试覆盖不足**: 主要依赖手动测试脚本

## 迁移目标
将整个Flask应用迁移到FastAPI，要求：
- 保持所有API接口完全兼容
- 保持现有业务逻辑不变
- 保持数据库配置和连接方式
- 保持流式响应功能
- 利用FastAPI的原生异步支持简化架构
