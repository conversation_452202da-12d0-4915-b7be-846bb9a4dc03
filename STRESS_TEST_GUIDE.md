# ChatBI系统压力测试指南

## 🎯 测试目标

本压力测试系统专门为ChatBI设计，用于在qwen-turbo API限流条件下测试系统的最大并发用户支持能力。

### 核心功能
- **智能限流控制**: 自动避免触发API限流，确保测试连续性
- **真实场景模拟**: 使用实际业务查询和用户行为模式
- **全面性能监控**: 响应时间、QPS、成功率、系统资源等多维度监控
- **详细报告生成**: 自动生成包含图表的HTML报告和优化建议

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖包
pip install -r requirements.txt

# 确保ChatBI服务正在运行
python run.py
```

### 2. 基础测试

```bash
# 运行默认配置的压力测试
python chatbi_stress_test.py

# 自定义配置测试
python chatbi_stress_test.py --max-users 500 --duration 3 --base-url http://localhost:5000
```

### 3. 查看结果

测试完成后会在`test_reports`目录下生成：
- HTML报告文件（包含图表和分析）
- JSON原始数据文件

## 📊 测试配置

### API限流配置
- **QPM限制**: 1,200次/分钟（qwen-turbo默认）
- **TPM限制**: 5,000,000 tokens/分钟
- **安全边界**: 使用限制的80%，避免触发限流

### 测试场景
1. **渐进式并发**: 10 → 50 → 100 → 200 → 500 → 1000用户
2. **真实查询**: 简单统计、复杂分析、多表关联等
3. **用户模式**: 休闲用户、业务分析师、数据科学家、高级用户

### 性能指标
- **响应时间**: 平均、P50、P95、P99分位数
- **吞吐量**: QPS、成功率、错误率
- **系统资源**: CPU、内存使用率
- **API使用**: 实际QPM、TPM消耗

## 🔧 高级配置

### 命令行参数

```bash
python chatbi_stress_test.py \
  --base-url http://localhost:5000 \    # 测试目标URL
  --max-users 1000 \                    # 最大并发用户数
  --duration 2 \                        # 每步测试持续时间(分钟)
  --qpm-limit 1200 \                    # QPM限制
  --tpm-limit 5000000                   # TPM限制
```

### 自定义测试场景

编辑`test_scenarios.py`文件，可以：
- 添加新的测试查询
- 调整用户行为模式
- 修改查询复杂度分布

### 监控告警配置

在`chatbi_stress_test.py`中的`_setup_alert_rules`方法中配置：
- 响应时间告警阈值
- 成功率告警阈值
- 系统资源告警阈值

## 📈 测试报告解读

### 关键指标说明

1. **最大并发用户数**: 系统能稳定支持的最大并发数
2. **峰值QPS**: 系统达到的最高每秒查询数
3. **平均响应时间**: 所有成功请求的平均响应时间
4. **总体成功率**: 所有请求中成功的比例

### 性能图表

1. **响应时间趋势**: 显示随并发数增加的响应时间变化
2. **QPS性能**: 显示系统吞吐量随并发数的变化
3. **成功率**: 显示系统稳定性随负载的变化
4. **系统资源**: 显示CPU和内存使用情况
5. **API限流**: 显示QPM和TPM的使用情况

### 优化建议

报告会根据测试结果自动生成优化建议：
- 数据库查询优化
- 缓存策略建议
- 系统资源扩容建议
- API限流优化建议

## 🛠️ 故障排除

### 常见问题

1. **连接错误**
   ```
   解决方案: 确保ChatBI服务正在运行，检查URL配置
   ```

2. **依赖包缺失**
   ```bash
   pip install aiohttp matplotlib seaborn psutil jinja2
   ```

3. **内存不足**
   ```
   解决方案: 减少最大并发用户数或增加系统内存
   ```

4. **API限流触发**
   ```
   解决方案: 降低安全边界比例或减少测试强度
   ```

### 日志分析

测试过程中的详细日志保存在`stress_test.log`文件中，包括：
- 测试进度信息
- 性能告警信息
- 错误和异常信息

## 🎯 最佳实践

### 测试前准备
1. 确保系统处于稳定状态
2. 清理日志和临时文件
3. 确认API密钥和配置正确
4. 预估测试时间和资源需求

### 测试执行
1. 从小规模开始，逐步增加负载
2. 监控系统资源使用情况
3. 注意API限流使用率
4. 及时停止异常测试

### 结果分析
1. 重点关注成功率和响应时间
2. 分析性能瓶颈点
3. 对比不同并发级别的表现
4. 制定优化计划

## 📋 测试检查清单

### 测试前
- [ ] ChatBI服务正常运行
- [ ] 依赖包已安装
- [ ] API配置正确
- [ ] 系统资源充足

### 测试中
- [ ] 监控测试进度
- [ ] 观察性能指标
- [ ] 注意告警信息
- [ ] 记录异常情况

### 测试后
- [ ] 查看HTML报告
- [ ] 分析性能数据
- [ ] 制定优化计划
- [ ] 备份测试结果

## 🔄 持续优化

### 定期测试
建议定期进行压力测试：
- 新功能发布后
- 系统配置变更后
- 性能优化后
- 负载模式变化后

### 基准对比
保存测试结果作为性能基准：
- 建立性能基线
- 对比优化效果
- 跟踪性能趋势
- 预测容量需求

## 📞 技术支持

如遇到问题，请检查：
1. 日志文件中的错误信息
2. 系统资源使用情况
3. 网络连接状态
4. API配置和限额

---

**注意**: 压力测试会对系统产生较高负载，建议在测试环境中进行，避免影响生产服务。
