from fastapi import FastAPI
from .middleware import setup_middleware
from .routes import api_router
from .services.schema_rag_initializer import sync_initialize_schema_system
import logging

logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    """FastAPI应用工厂函数。"""
    app = FastAPI(
        title="ChatBI API",
        description="智能数据分析平台API - 基于RAG的动态Schema感知",
        version="1.0.0",
        docs_url="/docs",  # Swagger UI
        redoc_url="/redoc"  # ReDoc
    )

    # 设置中间件（包括CORS）
    setup_middleware(app)

    # 注册API路由
    app.include_router(api_router, prefix="/api")

    # 初始化Schema RAG系统（后台异步执行）
    try:
        init_result = sync_initialize_schema_system()
        logger.info(f"Schema RAG系统初始化: {init_result}")
    except Exception as e:
        logger.warning(f"Schema RAG初始化失败: {e}")

    logger.info("FastAPI应用创建完成")
    return app