"""
FastAPI中间件配置

配置CORS和其他中间件，保持与Flask版本相同的行为。
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

logger = logging.getLogger(__name__)


def setup_cors(app: FastAPI) -> None:
    """
    配置CORS中间件，与原Flask-CORS配置保持一致
    """
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 与原Flask CORS(app)配置一致，允许所有源
        allow_credentials=True,
        allow_methods=["*"],  # 允许所有HTTP方法
        allow_headers=["*"],  # 允许所有请求头
    )
    logger.info("CORS中间件配置完成")


def setup_middleware(app: FastAPI) -> None:
    """
    设置所有中间件
    """
    setup_cors(app)
    
    # 可以在这里添加其他中间件
    # 例如：认证中间件、请求日志中间件等
    
    logger.info("所有中间件配置完成")
