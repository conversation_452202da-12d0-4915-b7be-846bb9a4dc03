"""
FastAPI Pydantic模型定义

定义所有API端点的请求和响应模型，确保类型安全和自动文档生成。
保持与原Flask版本完全相同的数据结构。
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict
from datetime import datetime


class SuggestionRequest(BaseModel):
    """输入联想建议请求模型"""
    query: str = Field(..., description="用户输入的查询前缀")


class SuggestionResponse(BaseModel):
    """输入联想建议响应模型"""
    suggestions: List[str] = Field(..., description="匹配的建议列表，最多5个")


class QueryRequest(BaseModel):
    """自然语言查询请求模型"""
    question: str = Field(..., description="用户的自然语言问题")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    details: Optional[str] = Field(None, description="详细错误信息")


# 流式响应的事件类型定义（用于文档说明）
class StreamEvent(BaseModel):
    """Server-Sent Events流式响应事件模型"""
    event: str = Field(..., description="事件类型")
    data: Dict[str, Any] = Field(..., description="事件数据")


class SummaryChunkEvent(BaseModel):
    """计划总结文本块事件"""
    chunk: str = Field(..., description="文本块内容")


class SqlGeneratedEvent(BaseModel):
    """SQL生成完成事件"""
    sql_query: str = Field(..., description="生成的SQL查询语句")


class DataFinalEvent(BaseModel):
    """最终数据事件"""
    raw_data: List[List[Any]] = Field(..., description="查询结果原始数据")
    metadata: Dict[str, Any] = Field(..., description="数据元信息")


class BitwiseDecodedEvent(BaseModel):
    """位运算字段解码事件"""
    bitwise_fields: List[str] = Field(..., description="位运算字段列表")
    decoded_data: List[List[Any]] = Field(..., description="解码后的数据")


class FinalSummaryStartEvent(BaseModel):
    """最终总结开始事件"""
    pass  # 空事件，仅作为信号


class FinalSummaryChunkEvent(BaseModel):
    """最终总结文本块事件"""
    chunk: str = Field(..., description="总结文本块")


class StreamErrorEvent(BaseModel):
    """流式处理错误事件"""
    source: str = Field(..., description="错误来源")
    message: str = Field(..., description="错误消息")
