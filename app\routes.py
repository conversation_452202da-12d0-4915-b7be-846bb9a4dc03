import json
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from .services.query_service import query_streaming_service, DateTimeEncoder
from .models import SuggestionRequest, SuggestionResponse, QueryRequest, ErrorResponse
from typing import Any, List, AsyncGenerator
import logging

logger = logging.getLogger(__name__)

# 创建FastAPI路由器
api_router = APIRouter()

# --- 输入联想建议部分 (保持不变) ---
SUGGESTION_LIST: List[str] = [
    "按学历统计专家数量分布",
    "按职称统计专家数量，并生成图表",
    "各个级别的专家有多少人？",
    "查询姓张的专家的姓名和邮箱",
    "列出所有研究员的姓名和研究方向",
    "开发状态为'开发成功'的专家有哪些？",
    "统计来自'艾思专家'和'智库专家'的专家数量",
    "最近一个月内新创建的专家是谁？",
    "查询所有是'院士'或'IEEE Fellow'的专家头衔和姓名"
]


def get_suggestions(query: str) -> List[str]:
    """
    根据用户输入的前缀，从建议列表中筛选匹配项。
    """
    if not query:
        return []
    # 使用 'in' 进行模糊匹配，忽略大小写
    return [
        suggestion for suggestion in SUGGESTION_LIST
        if query.lower() in suggestion.lower()
    ]


@api_router.post('/suggestions', response_model=SuggestionResponse)
async def handle_suggestions(request: SuggestionRequest):
    """
    处理输入联想建议的请求
    """
    try:
        suggestions = get_suggestions(request.query)
        return SuggestionResponse(suggestions=suggestions[:5])
    except Exception as e:
        logger.error(f"建议查询错误: {e}")
        raise HTTPException(status_code=500, detail="建议查询服务暂时不可用")


# --- 流式查询部分 (FastAPI原生异步实现) ---
@api_router.post('/query')
async def handle_query(request: QueryRequest):
    """
    处理来自前端的自然语言查询请求。
    使用FastAPI原生异步流式响应，消除复杂的线程+队列桥接逻辑。
    """
    try:
        async def generate_events():
            """
            FastAPI原生异步生成器，直接消费异步服务
            """
            try:
                async for item in query_streaming_service(request.question):
                    yield item
            except Exception as e:
                logger.error(f"流式查询服务错误: {e}")
                error_payload = json.dumps({
                    "error": "异步服务执行出错",
                    "details": str(e)
                }, cls=DateTimeEncoder)
                yield f"event: error\ndata: {error_payload}\n\n"

        return StreamingResponse(
            generate_events(),
            media_type='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
            }
        )
    except Exception as e:
        logger.error(f"查询请求处理错误: {e}")
        raise HTTPException(status_code=500, detail="查询服务暂时不可用")

# 注意：原有的SQL执行函数已移至services/query_service.py中统一管理
# 这里不再重复定义，保持代码的单一职责原则
