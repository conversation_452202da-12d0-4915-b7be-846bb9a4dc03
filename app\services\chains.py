"""
ChatBI查询服务的LCEL链模块。

这个模块定义了用于查询处理流程的LangChain Expression Language (LCEL)链。
每个链都有特定的责任：
1. sql_gen_chain: 从自然语言问题生成精确高效的SQL查询，支持多表关联和位运算字段
2. interpretation_chain: 根据用户的提问，立刻用自然流畅的语言告诉用户你将如何一步步为他分析数据
3. final_summary_chain: 根据查询结果生成最终的洞察报告

集成了schema_manager的表关系和位运算字段处理功能。
"""

import re
import asyncio
from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnableLambda

from .llm_clients import sql_llm, interpretation_llm
from .prompts import SQL_GEN_SYSTEM, INTERPRETATION_SYSTEM, FINAL_SUMMARY_SYSTEM
from .schema_embedding_service import get_schema_rag_orchestrator


def clean_sql(sql: str) -> str:
    """
    清洗SQL查询字符串，去除可能的Markdown代码块和额外的空格。

    Args:
        sql (str): 可能包含markdown格式的原始SQL字符串

    Returns:
        str: 清洗后可执行的SQL字符串
    """
    # 移除开头的```sql或```
    sql = re.sub(r"^```(?:sql)?\s*", "", sql, flags=re.IGNORECASE)
    # 移除结尾的```
    sql = re.sub(r"\s*```$", "", sql)
    # 清理行：去除空白字符并移除空行
    lines = [line.strip() for line in sql.splitlines()]
    return "\n".join([l for l in lines if l])


def validate_sql_syntax(sql: str) -> Dict[str, Any]:
    """
    验证SQL语法的基本正确性，特别是位运算字段的使用。

    Args:
        sql (str): 待验证的SQL字符串

    Returns:
        Dict[str, Any]: 包含验证结果和建议的字典
    """
    validation_result = {
        "is_valid": True,
        "warnings": [],
        "suggestions": []
    }

    # 检查位运算字段是否正确使用
    bitwise_pattern = r'\b(officer|title)\s*=\s*\d+'
    if re.search(bitwise_pattern, sql, re.IGNORECASE):
        validation_result["warnings"].append(
            "检测到位运算字段使用了等号(=)操作符，建议使用位运算语法 (field & value) = value"
        )
        validation_result["suggestions"].append(
            "将 'officer = 4' 改为 '(officer & 4) = 4'"
        )

    # 检查是否包含必要的表名
    if "erp_expert" not in sql.lower():
        validation_result["warnings"].append("查询中未包含主表 erp_expert")

    return validation_result


async def get_dynamic_schema_context(question: str) -> Dict[str, Any]:
    """获取动态schema上下文"""
    try:
        rag_orchestrator = get_schema_rag_orchestrator()
        context = await rag_orchestrator.get_context_for_query(question)
        return context
    except Exception as e:
        logger.error(f"Failed to get dynamic schema context: {str(e)}")
        return {
            "primary_tables": ["erp_expert"],
            "relevant_fields": [],
            "join_paths": [],
            "business_context": "使用基础专家表",
            "table_relationships": "",
            "field_details": ""
        }

def format_schema_context(context: Dict[str, Any]) -> str:
    """格式化schema上下文为文本"""
    if not context or not context.get("primary_tables"):
        return "使用基础专家表结构"
    
    lines = []
    
    # 相关表
    lines.append(f"主要相关表: {', '.join(context['primary_tables'])}")
    
    # 表结构详情
    for table_name in context["primary_tables"]:
        lines.append(f"\n{table_name}表:")
        # 这里应该从完整的schema文档中获取详细信息
        # 简化版本：返回基本结构
    
    return "\n".join(lines)

async def enhance_sql_with_context(sql: str, question: str) -> str:
    """
    基于RAG上下文增强SQL查询
    """
    sql_lower = sql.lower().strip()
    question_lower = question.lower().strip()
    
    # 定义不需要LIMIT的关键词
    no_limit_keywords = [
        'count(', 'sum(', 'avg(', 'max(', 'min(', '统计', '数量', '多少',
        '总数', '总计', 'group by', '分组', '聚合', 'distinct count'
    ]
    
    # 决策逻辑
    has_limit = 'limit' in sql_lower
    is_aggregate = any(keyword in sql_lower for keyword in no_limit_keywords)
    is_count_query = any(keyword in question_lower for keyword in ['统计', '数量', '多少', '总数', '总计'])
    
    if not has_limit:
        if not (is_aggregate or is_count_query):
            sql = sql.rstrip(';').rstrip() + ' LIMIT 100'
            if not sql.endswith(';'):
                sql += ';'
    
    return sql


# --- 链1: RAG增强的SQL生成链 ---
def create_rag_sql_chain():
    """创建RAG增强的SQL生成链"""

    async def process_sql_generation(inputs: Dict[str, Any]) -> str:
        """处理SQL生成的完整流程"""
        question = inputs["question"]
        
        try:
            # 1. 获取动态schema上下文
            schema_context = await get_dynamic_schema_context(question)
            
            # 2. 构建提示参数
            prompt_params = {
                "question": question,
                "relevant_tables": ", ".join(schema_context["primary_tables"]),
                "dynamic_schema_context": format_schema_context(schema_context),
                "table_relationships": "相关表之间的连接关系",
                "field_details": "字段详细信息"
            }
            
            # 3. 生成原始SQL
            prompt = ChatPromptTemplate.from_messages([("system", SQL_GEN_SYSTEM)])
            raw_sql = (prompt | sql_llm | StrOutputParser()).invoke(prompt_params)

            # 4. 清洗SQL
            cleaned_sql = clean_sql(raw_sql)

            # 5. 验证SQL语法
            validation = validate_sql_syntax(cleaned_sql)
            if validation["warnings"]:
                print(f"SQL验证警告: {validation['warnings']}")

            # 6. 基于上下文增强SQL
            enhanced_sql = await enhance_sql_with_context(cleaned_sql, question)

            return enhanced_sql
            
        except Exception as e:
            logger.error(f"SQL generation failed: {str(e)}")
            # 降级到基础查询
            return f"SELECT * FROM erp_expert LIMIT 100;"

    return RunnableLambda(process_sql_generation)

# 创建RAG增强的SQL生成链实例
sql_gen_chain = create_rag_sql_chain()


# --- 链2: 解读链 ---
# 目的：为用户提供关于分析计划的即时反馈
interpretation_prompt = ChatPromptTemplate.from_messages([("system", INTERPRETATION_SYSTEM)])
interpretation_chain = (
    {"question": lambda x: x["question"]}
    | interpretation_prompt
    | interpretation_llm
    | StrOutputParser()
)


# --- 链3: 最终总结链 ---
# 目的：基于实际查询结果生成有洞察力的总结
final_summary_prompt = ChatPromptTemplate.from_messages([("system", FINAL_SUMMARY_SYSTEM)])
final_summary_chain = (
      final_summary_prompt
    | interpretation_llm  # 为保持一致性使用解读LLM
    | StrOutputParser()
)
