import os
import logging
from dotenv import load_dotenv
from sqlalchemy import create_engine
from langchain_community.utilities import SQLDatabase
from .db_session import init_async_db_manager, AsyncDatabaseManager

logger = logging.getLogger(__name__)

def load_env():
    from pathlib import Path
    # 尝试多个可能的.env文件位置
    possible_paths = [
        Path(__file__).parent.parent.parent / '.env',  # 项目根目录
        Path(__file__).parent.parent / '.env',         # app目录的父目录
        Path('.env'),                                   # 当前工作目录
    ]

    for env_path in possible_paths:
        if env_path.exists():
            load_dotenv(dotenv_path=env_path, verbose=False)
            return

    load_dotenv(verbose=False)

load_env()

# 从环境变量中获取数据库连接URL
DB_URL = os.getenv("DB_URL")
if not DB_URL:
    raise ValueError("环境变量 DB_URL 未设置，请检查 .env 文件。")

# =====================================================
# 同步数据库配置（保持LangChain兼容性）
# =====================================================

# 创建同步 SQLAlchemy 引擎，用于LangChain
sync_engine = create_engine(DB_URL, future=True, echo=False)

# 通过 engine.url 提取连接属性
url = sync_engine.url
host = url.host
port = url.port
user = url.username
password = url.password
database = url.database

# 创建 LangChain 的 SQLDatabase 实例（使用同步引擎）
db = SQLDatabase(engine=sync_engine)

# 将连接属性挂在 db 对象上，方便直接使用
setattr(db, 'host', host)
setattr(db, 'port', port)
setattr(db, 'user', user)
setattr(db, 'password', password)
setattr(db, 'database', database)

# =====================================================
# 异步数据库配置（用于高性能查询操作）
# =====================================================

# 初始化异步数据库管理器
async_db_manager = init_async_db_manager(DB_URL)

logger.info("数据库配置完成:")
logger.info(f"  - 同步引擎: {DB_URL} (用于LangChain)")
logger.info(f"  - 异步引擎: {async_db_manager.async_url} (用于查询操作)")

# 导出的对象
# - db: LangChain SQLDatabase实例（同步）
# - sync_engine: 同步SQLAlchemy引擎
# - async_db_manager: 异步数据库管理器
