"""
异步数据库会话管理器

提供统一的异步数据库会话管理，包括连接池优化、事务管理和错误处理。
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Dict, Any, List, Tuple, Optional
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy import text
from datetime import date, datetime
from decimal import Decimal
import json

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """
    自定义JSON编码器，用于处理日期、时间和Decimal类型的序列化。
    """
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)


class AsyncDatabaseManager:
    """异步数据库管理器"""
    
    def __init__(self, database_url: str):
        """
        初始化异步数据库管理器
        
        Args:
            database_url: 数据库连接URL
        """
        # 将PyMySQL URL转换为aiomysql URL
        if "mysql+pymysql://" in database_url:
            self.async_url = database_url.replace("mysql+pymysql://", "mysql+aiomysql://")
        else:
            self.async_url = database_url
            
        # 创建异步引擎
        self.async_engine = create_async_engine(
            self.async_url,
            echo=False,  # 生产环境设为False
            pool_size=10,  # 连接池大小
            max_overflow=20,  # 最大溢出连接数
            pool_pre_ping=True,  # 连接前ping检查
            pool_recycle=3600,  # 连接回收时间（秒）
        )
        
        # 创建异步会话工厂
        self.async_session_factory = async_sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        logger.info(f"异步数据库管理器初始化完成: {self.async_url}")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取异步数据库会话的上下文管理器
        
        Yields:
            AsyncSession: 异步数据库会话
        """
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"数据库会话错误: {e}")
                raise
            finally:
                await session.close()
    
    async def execute_query(self, sql: str) -> Dict[str, Any]:
        """
        执行SQL查询并返回结果，包含列名信息。
        
        Args:
            sql (str): 要执行的SQL查询语句
            
        Returns:
            Dict[str, Any]: 包含结果和列名的字典
        """
        try:
            async with self.get_session() as session:
                # 执行查询
                result = await session.execute(text(sql))
                
                # 获取列名
                column_names = list(result.keys()) if result.keys() else []
                
                # 获取所有数据
                rows = result.fetchall()
                
                # 转换为元组列表格式，保持与原函数相同的返回格式
                if rows:
                    data = [tuple(row) for row in rows]
                    return {
                        "data": data,
                        "columns": column_names
                    }
                else:
                    return {
                        "data": [],
                        "columns": column_names
                    }
                    
        except Exception as e:
            logger.error(f"SQL执行错误: {e}")
            # 返回与原函数相同格式的错误信息
            return {
                "data": [("SQL_EXECUTION_ERROR", str(e))],
                "columns": ["error", "message"]
            }
    
    async def execute_query_dict(self, sql: str) -> List[Dict[str, Any]]:
        """
        执行SQL查询并返回字典格式结果
        
        Args:
            sql (str): 要执行的SQL查询语句
            
        Returns:
            List[Dict[str, Any]]: 字典格式的查询结果
        """
        try:
            async with self.get_session() as session:
                result = await session.execute(text(sql))
                
                # 获取列名
                column_names = list(result.keys()) if result.keys() else []
                
                # 获取所有数据并转换为字典格式
                rows = result.fetchall()
                dict_results = []
                
                for row in rows:
                    row_dict = {}
                    for i, column_name in enumerate(column_names):
                        row_dict[column_name] = row[i] if i < len(row) else None
                    dict_results.append(row_dict)
                
                return dict_results
                
        except Exception as e:
            logger.error(f"SQL执行错误: {e}")
            return [{"error": "SQL_EXECUTION_ERROR", "message": str(e)}]
    
    async def test_connection(self) -> bool:
        """
        测试数据库连接是否正常
        
        Returns:
            bool: 连接是否成功
        """
        try:
            async with self.get_session() as session:
                await session.execute(text("SELECT 1"))
                logger.info("异步数据库连接测试成功")
                return True
        except Exception as e:
            logger.error(f"异步数据库连接测试失败: {e}")
            return False
    
    async def close(self):
        """关闭数据库引擎"""
        await self.async_engine.dispose()
        logger.info("异步数据库引擎已关闭")


# 全局异步数据库管理器实例（将在database.py中初始化）
async_db_manager: Optional[AsyncDatabaseManager] = None


async def get_async_db_manager() -> AsyncDatabaseManager:
    """
    获取全局异步数据库管理器实例
    
    Returns:
        AsyncDatabaseManager: 异步数据库管理器
    """
    global async_db_manager
    if async_db_manager is None:
        raise RuntimeError("异步数据库管理器未初始化，请先调用init_async_db_manager()")
    return async_db_manager


def init_async_db_manager(database_url: str) -> AsyncDatabaseManager:
    """
    初始化全局异步数据库管理器
    
    Args:
        database_url: 数据库连接URL
        
    Returns:
        AsyncDatabaseManager: 初始化的异步数据库管理器
    """
    global async_db_manager
    async_db_manager = AsyncDatabaseManager(database_url)
    return async_db_manager
