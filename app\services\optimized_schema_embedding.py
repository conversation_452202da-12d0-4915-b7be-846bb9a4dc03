"""
优化的Schema嵌入服务 - 解决臃肿问题
"""

import os
import asyncio
from typing import Dict, List, Any, Optional
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

from app.services.schema_text_generator import SchemaTextGenerator
from config.schema_config import SchemaConfig

logger = logging.getLogger(__name__)

class OptimizedEmbeddingService:
    """优化的嵌入服务"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY", "")
        self.text_generator = SchemaTextGenerator()
        self.config = SchemaConfig()
        
        try:
            from openai import OpenAI
            self.client = OpenAI(
                api_key=self.api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
            )
        except ImportError:
            logger.error("需要安装openai库: pip install openai")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本的嵌入向量"""
        if not texts:
            return []
            
        try:
            loop = asyncio.get_event_loop()
            
            def sync_embed():
                return self.client.embeddings.create(
                    model=self.config.EMBEDDING_CONFIG["model"],
                    input=texts,
                    dimensions=self.config.EMBEDDING_CONFIG["dimension"],
                    encoding_format="float"
                )
            
            response = await loop.run_in_executor(None, sync_embed)
            
            embeddings = [item.embedding for item in response.data]
            logger.info(f"成功获取{len(embeddings)}个嵌入向量")
            return embeddings
                
        except Exception as e:
            logger.error(f"百炼API调用失败: {str(e)}")
            raise

class OptimizedVectorStore:
    """优化的向量存储"""
    
    def __init__(self, embedding_service: OptimizedEmbeddingService, chroma_client):
        self.embedding_service = embedding_service
        self.chroma_client = chroma_client
        self.collection = chroma_client.client.get_or_create_collection(
            name=self.embedding_service.config.EMBEDDING_CONFIG["collection_name"],
            metadata={"hnsw:space": "cosine"}
        )
    
    async def index_schema_batch(self, schema_documents: Dict[str, Dict[str, Any]], 
                                use_optimized: bool = True) -> None:
        """批量索引schema文档 - 优化版本"""
        try:
            texts = []
            metadatas = []
            ids = []
            
            for table_name, document in schema_documents.items():
                if use_optimized:
                    # 使用优化的单一文本
                    result = self.embedding_service.text_generator.generate_optimized_text(document)
                    texts.append(result['text'])
                    metadatas.append({
                        "table_name": table_name,
                        "content_type": result['content_type'],
                        "document_type": "schema"
                    })
                    ids.append(f"{table_name}_optimized")
                else:
                    # 使用传统多文本方式（兼容旧逻辑）
                    results = self.embedding_service.text_generator.generate_all_texts(document)
                    for result in results:
                        texts.append(result['text'])
                        metadatas.append({
                            "table_name": table_name,
                            "content_type": result['content_type'],
                            "document_type": "schema"
                        })
                        ids.append(f"{table_name}_{result['content_type']}")
            
            if not texts:
                logger.warning("没有需要索引的文本")
                return
            
            # 优化批处理
            batch_size = self.embedding_service.config.EMBEDDING_CONFIG["batch_size"]
            total_processed = 0
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]
                batch_metadatas = metadatas[i:i+batch_size]
                batch_ids = ids[i:i+batch_size]
                
                batch_embeddings = await self.embedding_service.get_embeddings(batch_texts)
                
                if len(batch_embeddings) != len(batch_texts):
                    logger.error(f"批处理嵌入数量不匹配: {len(batch_embeddings)} != {len(batch_texts)}")
                    continue
                
                self.collection.add(
                    embeddings=batch_embeddings,
                    documents=batch_texts,
                    metadatas=batch_metadatas,
                    ids=batch_ids
                )
                
                total_processed += len(batch_texts)
                logger.info(f"已处理: {total_processed}/{len(texts)} 文档")
            
            logger.info(f"索引完成，共处理{total_processed}个文档")
            
        except Exception as e:
            logger.error(f"索引失败: {str(e)}")
            raise
    
    async def search_relevant_tables(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关表"""
        try:
            from app.services.schema_embedding_service import BailianEmbeddingService
            temp_service = BailianEmbeddingService()
            
            query_embedding = await temp_service.get_single_embedding(query)
            
            if not query_embedding:
                return []
            
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["metadatas", "documents", "distances"]
            )
            
            formatted_results = []
            for metadata, document, distance in zip(
                results["metadatas"][0], 
                results["documents"][0], 
                results["distances"][0]
            ):
                formatted_results.append({
                    "table_name": metadata.get("table_name", "unknown"),
                    "content_type": metadata.get("content_type", "unknown"),
                    "document": document,
                    "relevance_score": 1 - distance,
                    "metadata": metadata
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return []

class OptimizedRAGManager:
    """优化的RAG管理器 - 单一职责"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.embedding_service = OptimizedEmbeddingService()
        self.vector_store = None
        self.chroma_client = None
    
    async def initialize(self):
        """初始化RAG系统"""
        from app.services.schema_rag import schema_rag
        from app.services.schema_document_builder import SchemaDocumentBuilder
        
        self.chroma_client = schema_rag
        self.vector_store = OptimizedVectorStore(self.embedding_service, schema_rag)
        self.document_builder = SchemaDocumentBuilder(self.db_config)
        
        logger.info("优化的RAG系统已初始化")
    
    async def rebuild_index(self, use_optimized: bool = True) -> int:
        """重建索引 - 精简版本"""
        try:
            # 清空旧集合
            try:
                self.chroma_client.client.delete_collection(
                    self.embedding_service.config.EMBEDDING_CONFIG["collection_name"]
                )
            except Exception:
                pass
            
            # 重新创建集合
            self.vector_store.collection = self.chroma_client.client.get_or_create_collection(
                name=self.embedding_service.config.EMBEDDING_CONFIG["collection_name"],
                metadata={"hnsw:space": "cosine"}
            )
            
            # 构建schema文档
            schemas = self.document_builder.build_all_schemas(
                self.embedding_service.config.TARGET_TABLES
            )
            
            # 批量索引
            await self.vector_store.index_schema_batch(schemas, use_optimized=use_optimized)
            
            logger.info(f"索引重建完成，共索引{len(schemas)}个表")
            return len(schemas)
            
        except Exception as e:
            logger.error(f"重建索引失败: {str(e)}")
            raise
    
    async def get_context_for_query(self, query: str) -> Dict[str, Any]:
        """获取查询上下文"""
        if not self.vector_store:
            await self.initialize()
        
        search_results = await self.vector_store.search_relevant_tables(query)
        
        if not search_results:
            return {
                "primary_tables": [],
                "relevant_fields": [],
                "join_paths": [],
                "business_context": ""
            }
        
        # 提取唯一表名并按相关性排序
        table_scores = {}
        for result in search_results:
            table_name = result["table_name"]
            score = result["relevance_score"]
            table_scores[table_name] = max(table_scores.get(table_name, 0), score)
        
        sorted_tables = sorted(table_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            "primary_tables": [table[0] for table in sorted_tables],
            "relevant_fields": [],
            "join_paths": [],
            "business_context": "基于查询意图匹配的相关表",
            "confidence_scores": {table[0]: table[1] for table in sorted_tables}
        }

# 全局实例
optimized_rag_manager = None

def get_optimized_rag_manager() -> OptimizedRAGManager:
    """获取优化的RAG管理器"""
    global optimized_rag_manager
    if optimized_rag_manager is None:
        db_config = {
            'host': 'dev.ais.cn',
            'user': 'erp_select',
            'password': 'erp_select',
            'database': 'scholar_erp',
            'port': 3306
        }
        optimized_rag_manager = OptimizedRAGManager(db_config)
    return optimized_rag_manager