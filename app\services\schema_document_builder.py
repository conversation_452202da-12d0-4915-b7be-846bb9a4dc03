"""
Schema文档生成器
从数据库实时提取表结构、样本数据、业务规则，生成富结构化文档
"""

import json
import sqlite3
import pymysql
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import random

logger = logging.getLogger(__name__)

class SchemaDocumentBuilder:
    """从数据库提取丰富schema信息的文档生成器"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.connection = None
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            logger.info("Database connection established")
        except Exception as e:
            logger.error(f"Database connection failed: {str(e)}")
            raise
    
    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        with self.connection.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            return [list(table.values())[0] for table in tables]
    
    def build_table_document(self, table_name: str) -> Dict[str, Any]:
        """构建单个表的完整schema文档"""
        try:
            document = {
                "table_name": table_name,
                "description": self._get_table_description(table_name),
                "business_context": self._get_business_context(table_name),
                "fields": self._extract_fields_info(table_name),
                "relationships": self._extract_relationships(table_name),
                "query_patterns": self._generate_query_patterns(table_name),
                "created_at": datetime.now().isoformat()
            }
            return document
            
        except Exception as e:
            logger.error(f"Failed to build document for {table_name}: {str(e)}")
            return {"table_name": table_name, "error": str(e)}
    
    def _get_table_description(self, table_name: str) -> str:
        """获取表描述"""
        # 从表注释或预定义映射中获取
        descriptions = {
            "erp_expert": "专家主信息表，存储专家的基本信息和核心属性",
            "erp_expert_info": "专家详细信息表，包含教育背景、工作经历等扩展信息",
            "erp_expert_apply": "专家申请记录表，记录所有专家合作申请历史",
            "erp_expert_dev": "专家开发记录表，跟踪专家的开发状态和进展",
            "erp_expert_work": "专家工作经历表，记录专家的职位履历",
            "erp_expert_resume": "专家简历信息表，存储详细的学术和职业背景",
            "erp_expert_favorite": "专家收藏记录表，记录用户对专家的收藏行为",
            "erp_expert_log": "专家日志记录表，记录用户对专家的操作日志",
            "erp_expert_search": "专家搜索记录表，记录用户对专家的搜索行为",
            "erp_expert_support": "专家支持记录表，记录用户对专家的支持行为",
            "erp_expert_tag": "专家标签记录表，记录用户对专家的标签行为"
        }
        return descriptions.get(table_name, f"{table_name}表")
    
    def _get_business_context(self, table_name: str) -> str:
        """获取业务上下文"""
        contexts = {
            "erp_expert": "所有专家相关业务的入口表，其他专家表都通过expertId关联到此表",
            "erp_expert_info": "专家信息的扩展存储，与主表一对一关系，提供详细信息",
            "erp_expert_apply": "核心业务表，记录专家从申请到合作的完整生命周期",
            "erp_expert_dev": "专家开发过程跟踪，用于分析开发效率和成功率",
            "erp_expert_work": "专家职业履历分析，用于评估专家经验和能力"
        }
        return contexts.get(table_name, f"支持{table_name}的业务场景")
    
    def _extract_fields_info(self, table_name: str) -> List[Dict[str, Any]]:
        """提取字段详细信息"""
        fields = []
        
        with self.connection.cursor() as cursor:
            # 获取字段信息
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            # 获取外键信息
            foreign_keys = self._get_foreign_keys(table_name)
            
            for col in columns:
                field_info = {
                    "field_name": col['Field'],
                    "data_type": self._map_mysql_type(col['Type']),
                    "description": self._get_field_description(table_name, col['Field']),
                    "is_primary_key": col['Key'] == 'PRI',
                    "is_foreign_key": col['Field'] in foreign_keys,
                    "foreign_key_to": foreign_keys.get(col['Field']),
                    "is_nullable": col['Null'] == 'YES',
                    "default_value": col['Default']
                }
                
                # 添加枚举值、样本值、业务规则
                field_info.update(self._enrich_field_details(table_name, col['Field'], col['Type']))
                
                fields.append(field_info)
        
        return fields
    
    def _get_foreign_keys(self, table_name: str) -> Dict[str, str]:
        """获取外键信息"""
        foreign_keys = {}
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                    WHERE TABLE_NAME = '{table_name}' 
                    AND TABLE_SCHEMA = '{self.db_config['database']}'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                """)
                results = cursor.fetchall()
                for row in results:
                    foreign_keys[row['COLUMN_NAME']] = f"{row['REFERENCED_TABLE_NAME']}.{row['REFERENCED_COLUMN_NAME']}"
        except Exception as e:
            logger.warning(f"Failed to get foreign keys for {table_name}: {str(e)}")
        return foreign_keys
    
    def _map_mysql_type(self, mysql_type: str) -> str:
        """映射MySQL类型到标准类型"""
        type_lower = mysql_type.lower()
        
        if 'int' in type_lower:
            return 'integer'
        elif 'varchar' in type_lower or 'text' in type_lower:
            return 'string'
        elif 'datetime' in type_lower or 'timestamp' in type_lower:
            return 'datetime'
        elif 'decimal' in type_lower or 'float' in type_lower or 'double' in type_lower:
            return 'decimal'
        elif 'tinyint(1)' in type_lower:
            return 'boolean'
        elif 'tinyint' in type_lower:
            return 'enum'
        else:
            return 'unknown'
    
    def _get_field_description(self, table_name: str, field_name: str) -> str:
        """获取字段描述"""
        # 预定义的字段描述映射
        field_descriptions = {
            "erp_expert": {
                "status": "专家开发状态，决定专家是否可以被推荐",
                "officer": "专家类型位运算字段，支持多角色标签",
                "title": "专家头衔位运算字段，标识专家学术地位",
                "domain": "专家专业领域，用于按学科分类",
                "professional": "专家职称，反映专业水平",
                "createTime": "记录创建时间，用于按时间分析",
                "updateTime": "最后更新时间，跟踪数据新鲜度"
            }
        }
        
        return field_descriptions.get(table_name, {}).get(field_name, f"{field_name}字段")
    
    def _enrich_field_details(self, table_name: str, field_name: str, mysql_type: str) -> Dict[str, Any]:
        """丰富字段详细信息"""
        details = {
            "enum_values": None,
            "sample_values": [],
            "business_rules": None
        }
        
        # 枚举值映射
        enum_mappings = {
            "erp_expert": {
                "status": {
                    "1": "未开发",
                    "2": "待启动",
                    "3": "开发中", 
                    "4": "审核中",
                    "5": "开发成功",
                    "6": "开发失败",
                    "7": "开发异常",
                    "8": "待补充"
                },
                "sex": {"1": "男", "2": "女"},
                "professional": {
                    "1": "教授", "2": "副教授", "3": "助理教授", "4": "研究员",
                    "5": "副研究员", "6": "助理研究员", "7": "正高级工程师",
                    "8": "高级工程师", "9": "讲师", "10": "学生"
                },
                "pf": {
                    "1": "艾思专家", "2": "智库专家", "3": "论文作者",
                    "4": "导师数据库", "5": "专家总库新增/导入"
                },
                "domain": {
                    "1": "环境及资源科学技术",
                    "2": "能源科学",
                    "3": "化学",
                    "4": "计算机科学",
                    "5": "通信与电子工程",
                    "6": "数学",
                    "7": "物理学",
                    "8": "材料科学",
                    "9": "土木与建筑工程",
                    "10": "交通运输工程",
                    "11": "机械工程",
                    "12": "电气工程",
                    "13": "控制科学与工程",
                    "14": "仪器科学与技术",
                    "15": "化学工程与技术",
                    "16": "矿业工程",
                    "17": "石油与天然气工程",
                    "18": "纺织科学与工程",
                    "19": "水利工程",
                    "20": "其他学科",
                    "21": "环境科学",
                    "22": "心理学",
                    "23": "语言教育艺术体育",
                    "24": "经济学",
                    "25": "管理学",
                    "26": "动力与电气工程",
                    "27": "力学与物理学",
                    "28": "测绘科学",
                    "29": "航空航天科学",
                    "30": "纺织科学",
                    "31": "安全科学",
                    "32": "农林畜牧水产",
                    "33": "机械工程",
                    "34": "冶金工程",
                    "35": "生物学与生物工程",
                    "36": "地理科学",
                    "37": "天文学",
                    "38": "民族宗教",
                    "39": "医学与药学",
                    "40": "核科学",
                    "41": "文学历史哲学",
                    "42": "政治学",
                    "43": "法学",
                    "44": "海洋科学",
                    "45": "光学",
                    "46": "教育学"
                },
                "purpose": {
                    "1": "学术合作",
                    "2": "项目合作",
                    "3": "技术咨询",
                    "4": "成果转化",
                    "5": "人才培养"
                },
                "level": {
                    "0": "L0",
                    "1": "L1",
                    "2": "L2",
                    "3": "L3",
                    "4": "L4",
                    "5": "L5",
                    "6": "L6",
                    "7": "L7"
                },
                "education": {
                    "1": "本科以下",
                    "2": "本科",
                    "3": "研究生",
                    "4": "博士及以上"
                },
                "tags": {
                    "1": "校级领导",
                    "2": "院所领导",
                    "3": "处级领导",
                    "4": "学协会领导",
                    "5": "学科带头人",
                    "6": "QS Top 200",
                    "7": "QS Top 500",
                    "8": "双一流高校",
                    "9": "985高校",
                    "10": "本科院校",
                    "11": "专科院校",
                    "12": "公办",
                    "13": "民办",
                    "14": "合作办学",
                    "15": "期刊任职",
                    "16": "学术会议组织经验",
                    "17": "学术评审经验",
                    "18": "审修经历",
                    "19": "学术文章原创经验",
                    "20": "顶级学术机构",
                    "21": "导师师资",
                    "22": "深度话题创作",
                    "23": "一般话题创作",
                    "24": "专业曝光",
                    "25": "社交拓展",
                    "26": "知识分享",
                    "27": "期刊审稿",
                    "28": "同行预审",
                    "29": "翻译",
                    "30": "润色",
                    "31": "审修",
                    "32": "降重",
                    "33": "头条创作",
                    "34": "提案",
                    "35": "会议嘉宾",
                    "36": "艾思云课堂",
                    "37": "论文—对一",
                    "38": "其它",
                    "39": "会议组委审稿",
                    "40": "专家审稿",
                    "41": "校对",
                    "42": "排版",
                    "43": "图表编辑",
                    "44": "合作办会",
                    "45": "经费筹集",
                    "46": "专家推荐",
                    "47": "场地申请",
                    "48": "合作引荐",
                    "49": "宣传组织",
                    "50": "会务筹备",
                    "51": "志愿者组织",
                    "52": "参会推荐",
                    "53": "投稿推荐",
                    "54": "出版配合",
                    "55": "翻译水平高",
                    "56": "润色水平高",
                    "57": "合作意愿强",
                    "58": "配合程度高",
                    "59": "有亲和力",
                    "60": "学术资源丰富",
                    "61": "时间观念强",
                    "62": "任务质量高",
                    "63": "学术水平高",
                    "64": "翻译水平低",
                    "65": "润色水平低",
                    "66": "合作意愿弱",
                    "67": "配合程度低",
                    "68": "不好相处",
                    "69": "学术资源欠缺",
                    "70": "时间观念差",
                    "71": "任务质量低",
                    "72": "学术水平低",
                    "73": "海外院校",
                    "74": "终止合作",
                    "75": "审批文件"
                }
            }
        }
        
        # 位运算映射
        bitwise_mappings = {
            "erp_expert": {
                "officer": {
                    "4": "审稿专家",
                    "8": "编译专家", 
                    "16": "课程导师",
                    "32": "会议嘉宾",
                    "64": "头条创作者"
                },
                "title": {
                    "1": "院士",
                    "2": "国家级高层次人才",
                    "4": "国家级青年人才",
                    "8": "IEEE Fellow",
                    "16": "ACM Fellow",
                    "32": "IEEE高级会员",
                    "64": "ACM高级会员"
                }
            }
        }
        
        # 设置枚举值
        if table_name in enum_mappings and field_name in enum_mappings[table_name]:
            details["enum_values"] = enum_mappings[table_name][field_name]
        elif table_name in bitwise_mappings and field_name in bitwise_mappings[table_name]:
            details["enum_values"] = bitwise_mappings[table_name][field_name]
        
        # 获取样本值
        details["sample_values"] = self._get_sample_values(table_name, field_name, 3)
        
        # 设置业务规则
        details["business_rules"] = self._get_business_rules(table_name, field_name)
        
        return details
    
    def _get_sample_values(self, table_name: str, field_name: str, limit: int = 3) -> List[str]:
        """获取字段的样本值"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT DISTINCT {field_name} 
                    FROM {table_name} 
                    WHERE {field_name} IS NOT NULL 
                    AND {field_name} != '' 
                    LIMIT {limit}
                """)
                results = cursor.fetchall()
                return [str(row[field_name]) for row in results if row[field_name] is not None]
        except Exception as e:
            logger.warning(f"Failed to get sample values for {table_name}.{field_name}: {str(e)}")
            return []
    
    def _get_business_rules(self, table_name: str, field_name: str) -> Optional[str]:
        """获取业务规则"""
        rules = {
            "erp_expert": {
                "status": "只有status=5的开发成功专家才能被推荐给客户合作",
                "officer": "使用位运算&进行筛选，如(officer & 4) = 4表示审稿专家",
                "title": "院士(title & 1 = 1)为最高级别专家，优先推荐",
                "createTime": "按时间分析专家增长趋势，用于业务决策"
            }
        }
        return rules.get(table_name, {}).get(field_name)
    
    def _extract_relationships(self, table_name: str) -> List[Dict[str, Any]]:
        """提取表关联关系"""
        relationships = []
        
        # 预定义的关系映射
        relationship_map = {
            "erp_expert": [
                {
                    "target_table": "erp_expert_info",
                    "type": "one_to_one",
                    "join_condition": "erp_expert.id = erp_expert_info.expertId",
                    "description": "专家详细信息扩展，一对一关系"
                },
                {
                    "target_table": "erp_expert_apply",
                    "type": "one_to_many",
                    "join_condition": "erp_expert.id = erp_expert_apply.expertId",
                    "description": "专家申请记录，一个专家多条申请"
                },
                {
                    "target_table": "erp_expert_dev",
                    "type": "one_to_many",
                    "join_condition": "erp_expert.id = erp_expert_dev.expertId",
                    "description": "专家开发记录，跟踪开发过程"
                },
                {
                    "target_table": "erp_expert_work",
                    "type": "one_to_many",
                    "join_condition": "erp_expert.id = erp_expert_work.expertId",
                    "description": "专家工作经历，记录职业履历"
                }
            ]
        }
        
        return relationship_map.get(table_name, [])
    
    def _generate_query_patterns(self, table_name: str) -> List[str]:
        """生成典型查询模式"""
        patterns_map = {
            "erp_expert": [
                "按状态筛选专家：WHERE status = 5",
                "按领域分组统计：SELECT domain, COUNT(*) FROM erp_expert GROUP BY domain",
                "按时间排序：SELECT * FROM erp_expert ORDER BY createTime DESC LIMIT 100",
                "位运算筛选：SELECT * FROM erp_expert WHERE (officer & 4) = 4",
                "模糊搜索：SELECT * FROM erp_expert WHERE surname LIKE '%张%'",
                "关联查询：SELECT e.*, ei.education FROM erp_expert e JOIN erp_expert_info ei ON e.id = ei.expertId"
            ]
        }
        return patterns_map.get(table_name, [])
    
    def build_all_schemas(self, target_tables: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """构建指定表的schema文档"""
        # 定义需要嵌入的表格列表
        TARGET_TABLES = [
            'erp_expert',
            'erp_expert_info',
            'erp_expert_apply',
            'erp_expert_dev',
            'erp_expert_work',
            'erp_expert_resume',
            'erp_expert_favorite',
            'erp_expert_log',
            'erp_expert_search',
            'erp_expert_support',
            'erp_expert_tag'
        ]
        
        self.connect()
        try:
            if target_tables:
                # 使用传入的表列表
                tables_to_process = target_tables
            else:
                # 使用预定义的TARGET_TABLES
                tables_to_process = TARGET_TABLES
            
            schemas = {}
            for table_name in tables_to_process:
                try:
                    logger.info(f"Building schema for {table_name}")
                    schemas[table_name] = self.build_table_document(table_name)
                except Exception as e:
                    logger.error(f"Failed to build schema for {table_name}: {str(e)}")
                    continue
            
            return schemas
            
        finally:
            self.disconnect()

# 使用示例
if __name__ == "__main__":
    from app.services.database import get_db_config
    
    db_config = get_db_config()
    builder = SchemaDocumentBuilder(db_config)
    
    # 构建所有schema
    all_schemas = builder.build_all_schemas()
    
    # 保存到文件（用于调试）
    with open('schemas_output.json', 'w', encoding='utf-8') as f:
        json.dump(all_schemas, f, ensure_ascii=False, indent=2)
    
    print(f"Generated schemas for {len(all_schemas)} tables")