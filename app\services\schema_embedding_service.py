"""
百炼平台嵌入服务集成
使用text-embedding-v4模型生成schema文档的向量表示
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

# 百炼API配置
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "sk-bd884acabfc8420fb852bbdd86fa276a")
EMBEDDING_MODEL = "text-embedding-v4"
EMBEDDING_DIMENSION = 1024

logger = logging.getLogger(__name__)

class BailianEmbeddingService:
    """百炼平台嵌入服务"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or DASHSCOPE_API_KEY
        try:
            from openai import OpenAI
            self.client = OpenAI(
                api_key=self.api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
            )
        except ImportError:
            logger.error("需要安装openai库: pip install openai")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """获取文本的嵌入向量"""
        if not texts:
            return []
            
        try:
            # 使用同步版本的OpenAI客户端，因为异步在Windows下可能有兼容性问题
            loop = asyncio.get_event_loop()
            
            def sync_embed():
                return self.client.embeddings.create(
                    model=EMBEDDING_MODEL,
                    input=texts,
                    dimensions=EMBEDDING_DIMENSION,
                    encoding_format="float"
                )
            
            # 在事件循环中运行同步调用
            response = await loop.run_in_executor(None, sync_embed)
            
            embeddings = []
            for item in response.data:
                embeddings.append(item.embedding)
            
            logger.info(f"成功获取{len(embeddings)}个嵌入向量，维度：{len(embeddings[0]) if embeddings else 0}")
            return embeddings
                
        except Exception as e:
            logger.error(f"百炼API调用失败: {str(e)}")
            raise
    
    async def get_single_embedding(self, text: str) -> List[float]:
        """获取单个文本的嵌入向量"""
        embeddings = await self.get_embeddings([text])
        return embeddings[0] if embeddings else []


class SchemaVectorStore:
    """Schema向量存储和检索系统"""
    
    def __init__(self, embedding_service: BailianEmbeddingService, chroma_client):
        self.embedding_service = embedding_service
        self.chroma_client = chroma_client
        # 直接获取集合，避免通过chroma_client.collection访问
        self.collection = chroma_client.client.get_or_create_collection(
            name="table_schemas_v2",
            metadata={"hnsw:space": "cosine"}
        )
    
    async def index_schema_batch(self, schema_documents: Dict[str, Dict[str, Any]]) -> None:
        """批量索引schema文档"""
        try:
            # 准备文本和元数据
            texts = []
            metadatas = []
            ids = []
            
            for table_name, document in schema_documents.items():
                # 生成不同粒度的文本用于嵌入
                texts.extend([
                    self._generate_table_text(document),
                    self._generate_fields_text(document),
                    self._generate_relationships_text(document)
                ])
                
                # 对应元数据
                metadatas.extend([
                    {
                        "table_name": table_name,
                        "content_type": "table_overview",
                        "document_type": "schema"
                    },
                    {
                        "table_name": table_name,
                        "content_type": "fields_detail",
                        "document_type": "schema"
                    },
                    {
                        "table_name": table_name,
                        "content_type": "relationships",
                        "document_type": "schema"
                    }
                ])
                
                ids.extend([
                    f"{table_name}_overview",
                    f"{table_name}_fields",
                    f"{table_name}_relationships"
                ])
            
            if not texts:
                logger.warning("No texts to index")
                return
            
            # 分批处理，避免API限制（每次最多10个）
            batch_size = 9  # 留一点余量
            total_processed = 0
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]
                batch_metadatas = metadatas[i:i+batch_size]
                batch_ids = ids[i:i+batch_size]
                
                # 获取当前批次的嵌入向量
                batch_embeddings = await self.embedding_service.get_embeddings(batch_texts)
                
                if len(batch_embeddings) != len(batch_texts):
                    logger.error(f"Batch embedding count mismatch: {len(batch_embeddings)} != {len(batch_texts)}")
                    continue
                
                # 存储当前批次到ChromaDB
                self.collection.add(
                    embeddings=batch_embeddings,
                    documents=batch_texts,
                    metadatas=batch_metadatas,
                    ids=batch_ids
                )
                
                total_processed += len(batch_texts)
                logger.info(f"Processed batch: {total_processed}/{len(texts)} documents")
            
            logger.info(f"Indexed {total_processed} schema documents")
            
        except Exception as e:
            logger.error(f"Failed to index schema batch: {str(e)}")
            raise
    
    def _generate_table_text(self, document: Dict[str, Any]) -> str:
        """生成表级描述文本"""
        desc = str(document['description'])[:100]
        context = str(document['business_context'])[:100]
        return f"表名: {document['table_name']} 描述: {desc} 业务场景: {context} 字段数: {len(document.get('fields', []))} 关联表数: {len(document.get('relationships', []))}"
    
    def _generate_fields_text(self, document: Dict[str, Any]) -> str:
        """生成字段级描述文本"""
        fields_text = []
        for field in document.get('fields', [])[:8]:  # 限制字段数量
            field_desc = f"字段{field['field_name']}({field['data_type']}): {str(field['description'])[:50]}"
            
            if field.get('enum_values'):
                enum_values = list(field['enum_values'].values())
                enum_str = str(enum_values[:3]) if len(enum_values) > 3 else str(enum_values)
                field_desc += f" 枚举: {enum_str}"
            
            if field.get('business_rules'):
                rule_str = str(field['business_rules'])[:40]
                field_desc += f" 规则: {rule_str}"
                
            if field.get('sample_values'):
                sample_str = str(field['sample_values'])[:30]
                field_desc += f" 样本: {sample_str}"
            
            fields_text.append(field_desc)
        
        result = " | ".join(fields_text)
        return result[:7800]  # 确保总长度不超过限制
    
    def _generate_relationships_text(self, document: Dict[str, Any]) -> str:
        """生成关系描述文本"""
        relationships = document.get('relationships', [])
        if not relationships:
            return f"表{document['table_name']}无关联关系"
        
        rel_text = []
        for rel in relationships[:3]:  # 限制关系数量
            desc = str(rel['description'])[:50]
            rel_text.append(f"{rel['type']}关联{rel['target_table']}: {desc}")
        
        result = " | ".join(rel_text)
        return result[:500]  # 限制总长度
    
    async def search_relevant_tables(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相关表"""
        try:
            # 获取查询嵌入
            query_embedding = await self.embedding_service.get_single_embedding(query)
            
            if not query_embedding:
                return []
            
            # 在ChromaDB中搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["metadatas", "documents", "distances"]
            )
            
            formatted_results = []
            for i, (metadata, document, distance) in enumerate(
                zip(results["metadatas"][0], results["documents"][0], results["distances"][0])
            ):
                formatted_results.append({
                    "table_name": metadata.get("table_name", "unknown"),
                    "content_type": metadata.get("content_type", "unknown"),
                    "document": document,
                    "relevance_score": 1 - distance,
                    "metadata": metadata
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return []
    
    async def get_enhanced_context(self, query: str) -> Dict[str, Any]:
        """获取增强的schema上下文"""
        search_results = await self.search_relevant_tables(query)
        
        if not search_results:
            return {
                "primary_tables": [],
                "relevant_fields": [],
                "join_paths": [],
                "business_context": ""
            }
        
        # 聚合结果
        table_scores = {}
        field_mentions = []
        relationships = []
        
        for result in search_results:
            table_name = result["table_name"]
            score = result["relevance_score"]
            
            if table_name not in table_scores:
                table_scores[table_name] = score
            else:
                table_scores[table_name] = max(table_scores[table_name], score)
        
        # 排序并返回前3个相关表
        sorted_tables = sorted(table_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            "primary_tables": [table[0] for table in sorted_tables],
            "relevant_fields": [],  # 后续可以从field级别提取
            "join_paths": [],  # 后续可以基于关系图生成
            "business_context": "基于查询意图匹配的相关表"
        }


class SchemaRAGOrchestrator:
    """Schema RAG系统协调器"""
    
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.embedding_service = BailianEmbeddingService()
        self.chroma_client = None  # 将从外部注入
        self.vector_store = None
        self.document_builder = None
    
    async def initialize(self):
        """初始化整个RAG系统"""
        from app.services.schema_rag import schema_rag
        from app.services.schema_document_builder import SchemaDocumentBuilder
        
        self.chroma_client = schema_rag
        self.vector_store = SchemaVectorStore(self.embedding_service, schema_rag)
        self.document_builder = SchemaDocumentBuilder(self.db_config)
        
        logger.info("Schema RAG system initialized")
    
    async def rebuild_index(self):
        """重建完整的schema索引"""
        try:
            # 定义需要嵌入的表格列表
            TARGET_TABLES = [
                'erp_expert',
                'erp_expert_info',
                'erp_expert_apply',
                'erp_expert_dev',
                'erp_expert_work',
                'erp_expert_resume',
                'erp_expert_favorite',
                'erp_expert_log',
                'erp_expert_search',
                'erp_expert_support',
                'erp_expert_tag'
            ]
            
            # 清空并重新创建集合
            try:
                self.chroma_client.client.delete_collection("table_schemas_v2")
            except Exception:
                pass  # 集合可能不存在
                
            collection = self.chroma_client.client.get_or_create_collection(
                name="table_schemas_v2",
                metadata={"hnsw:space": "cosine"}
            )
            
            # 构建指定表格的schema文档
            schemas = self.document_builder.build_all_schemas(TARGET_TABLES)
            
            # 直接索引，不使用SchemaVectorStore
            texts = []
            metadatas = []
            ids = []
            
            for table_name, document in schemas.items():
                # 生成不同粒度的文本用于嵌入
                texts.extend([
                    self._generate_table_text(document),
                    self._generate_fields_text(document),
                    self._generate_relationships_text(document)
                ])
                
                # 对应元数据
                metadatas.extend([
                    {
                        "table_name": table_name,
                        "content_type": "table_overview",
                        "document_type": "schema"
                    },
                    {
                        "table_name": table_name,
                        "content_type": "fields_detail",
                        "document_type": "schema"
                    },
                    {
                        "table_name": table_name,
                        "content_type": "relationships",
                        "document_type": "schema"
                    }
                ])
                
                ids.extend([
                    f"{table_name}_overview",
                    f"{table_name}_fields",
                    f"{table_name}_relationships"
                ])
            
            if not texts:
                logger.warning("No texts to index")
                return 0
            
            # 分批处理，避免API限制
            batch_size = 9
            total_processed = 0
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i+batch_size]
                batch_metadatas = metadatas[i:i+batch_size]
                batch_ids = ids[i:i+batch_size]
                
                # 获取当前批次的嵌入向量
                batch_embeddings = await self.embedding_service.get_embeddings(batch_texts)
                
                # 存储当前批次到ChromaDB
                collection.add(
                    embeddings=batch_embeddings,
                    documents=batch_texts,
                    metadatas=batch_metadatas,
                    ids=batch_ids
                )
                
                total_processed += len(batch_texts)
                logger.info(f"Processed batch: {total_processed}/{len(texts)} documents")
            
            logger.info(f"Rebuilt index for {len(schemas)} tables")
            return len(schemas)
            
        except Exception as e:
            logger.error(f"Failed to rebuild index: {str(e)}")
            raise
    
    def _generate_table_text(self, document: Dict[str, Any]) -> str:
        """生成表级描述文本"""
        desc = str(document['description'])[:100]
        context = str(document['business_context'])[:100]
        return f"表名: {document['table_name']} 描述: {desc} 业务场景: {context} 字段数: {len(document.get('fields', []))} 关联表数: {len(document.get('relationships', []))}"
    
    def _generate_fields_text(self, document: Dict[str, Any]) -> str:
        """生成字段级描述文本"""
        fields_text = []
        for field in document.get('fields', [])[:8]:  # 限制字段数量
            field_desc = f"字段{field['field_name']}({field['data_type']}): {str(field['description'])[:50]}"
            
            if field.get('enum_values'):
                enum_values = list(field['enum_values'].values())
                enum_str = str(enum_values[:3]) if len(enum_values) > 3 else str(enum_values)
                field_desc += f" 枚举: {enum_str}"
            
            if field.get('business_rules'):
                rule_str = str(field['business_rules'])[:40]
                field_desc += f" 规则: {rule_str}"
                
            if field.get('sample_values'):
                sample_str = str(field['sample_values'])[:30]
                field_desc += f" 样本: {sample_str}"
            
            fields_text.append(field_desc)
        
        result = " | ".join(fields_text)
        return result[:7800]  # 确保总长度不超过限制
    
    def _generate_relationships_text(self, document: Dict[str, Any]) -> str:
        """生成关系描述文本"""
        relationships = document.get('relationships', [])
        if not relationships:
            return f"表{document['table_name']}无关联关系"
        
        rel_text = []
        for rel in relationships[:3]:  # 限制关系数量
            desc = str(rel['description'])[:50]
            rel_text.append(f"{rel['type']}关联{rel['target_table']}: {desc}")
        
        result = " | ".join(rel_text)
        return result[:500]  # 限制总长度
    
    async def get_context_for_query(self, query: str) -> Dict[str, Any]:
        """为查询获取上下文"""
        if not self.vector_store:
            await self.initialize()
        
        return await self.vector_store.get_enhanced_context(query)

# 全局实例
schema_rag_orchestrator = None

def get_schema_rag_orchestrator() -> SchemaRAGOrchestrator:
    """获取全局RAG协调器实例"""
    global schema_rag_orchestrator
    if schema_rag_orchestrator is None:
        db_config = {
            'host': 'dev.ais.cn',
            'user': 'erp_select',
            'password': 'erp_select',
            'database': 'scholar_erp',
            'port': 3306
        }
        schema_rag_orchestrator = SchemaRAGOrchestrator(db_config)
    return schema_rag_orchestrator