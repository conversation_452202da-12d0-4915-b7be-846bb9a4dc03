"""
ChromaDB + 百炼平台 RAG系统，用于存储和检索表schema信息
"""

import os
import json
import sqlite3
import chromadb
from chromadb.config import Settings
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

# 百炼API配置
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY", "")
DASHSCOPE_BASE_URL = "https://dashscope.aliyuncs.com/api/v1"
EMBEDDING_MODEL = "text-embedding-v4"
EMBEDDING_DIMENSION = 1536

logger = logging.getLogger(__name__)

class ChromaSchemaRAG:
    """ChromaDB + 百炼平台的Schema RAG系统"""
    
    def __init__(self, persist_directory: str = "./data/chroma_db"):
        self.persist_directory = persist_directory
        
        # 创建持久化目录
        os.makedirs(persist_directory, exist_ok=True)
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # 获取或创建集合
        self.collection = self.client.get_or_create_collection(
            name="table_schemas_v2",
            metadata={"hnsw:space": "cosine"}
        )
        
        logger.info(f"ChromaSchemaRAG initialized with collection: {self.collection.name}")
    
    def add_schema_document(self, table_name: str, document: Dict[str, Any], embedding: List[float]) -> None:
        """添加表schema文档到向量数据库（使用外部嵌入）"""
        try:
            # 生成文档ID
            doc_id = f"table_{table_name}_{datetime.now().timestamp()}"
            
            # 构建向量化的文本内容
            vector_text = self._build_vector_text(document)
            
            # 准备元数据
            metadata = {
                "table_name": table_name,
                "document_type": "table_schema",
                "created_at": datetime.now().isoformat(),
                "field_names": json.dumps([f["field_name"] for f in document.get("fields", [])]),
                "relationship_count": str(len(document.get("relationships", [])))
            }
            
            # 使用外部提供的嵌入向量
            self.collection.add(
                embeddings=[embedding],
                documents=[vector_text],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"Added schema document for table: {table_name}")
            
        except Exception as e:
            logger.error(f"Failed to add schema document for {table_name}: {str(e)}")
            raise
    
    def _build_vector_text(self, document: Dict[str, Any]) -> str:
        """构建用于向量化的文本内容"""
        parts = []
        
        # 表基本信息
        parts.append(f"表名: {document['table_name']}")
        parts.append(f"描述: {document['description']}")
        parts.append(f"业务场景: {document['business_context']}")
        
        # 字段信息
        parts.append("字段信息:")
        for field in document.get("fields", []):
            field_text = f"字段{field['field_name']} - {field['description']}"
            if field.get("enum_values"):
                field_text += f" 取值: {list(field['enum_values'].values())}"
            if field.get("business_rules"):
                field_text += f" 规则: {field['business_rules']}"
            parts.append(field_text)
        
        # 关系信息
        if document.get("relationships"):
            parts.append("关联关系:")
            for rel in document["relationships"]:
                parts.append(f"与{rel['target_table']} - {rel['description']}")
        
        return " ".join(parts)
    
    def search_relevant_tables(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索相关的表schema"""
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k,
                include=["metadatas", "documents", "distances"]
            )
            
            formatted_results = []
            for i, (metadata, document, distance) in enumerate(
                zip(results["metadatas"][0], results["documents"][0], results["distances"][0])
            ):
                formatted_results.append({
                    "table_name": metadata["table_name"],
                    "document": document,
                    "relevance_score": 1 - distance,
                    "metadata": metadata
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return []
    
    def get_table_schema(self, table_name: str) -> Optional[Dict[str, Any]]:
        """获取指定表的完整schema"""
        try:
            results = self.collection.get(
                where={"table_name": table_name},
                limit=1
            )
            
            if results and results["documents"]:
                # 这里应该返回完整的结构化数据
                # 实际实现中需要从原始存储获取
                return {"table_name": table_name, "found": True}
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get schema for {table_name}: {str(e)}")
            return None
    
    def clear_all_schemas(self) -> None:
        """清空所有schema文档（用于重建）"""
        try:
            self.client.delete_collection("table_schemas_v2")
            self.collection = self.client.get_or_create_collection(
                name="table_schemas_v2",
                metadata={"hnsw:space": "cosine"}
            )
            logger.info("Cleared all schema documents")
        except Exception as e:
            logger.error(f"Failed to clear schemas: {str(e)}")

# 全局实例
schema_rag = ChromaSchemaRAG()