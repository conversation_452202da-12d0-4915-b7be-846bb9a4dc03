"""
Schema RAG系统初始化脚本
用于首次启动时构建和更新schema索引
"""

import asyncio
import logging
from typing import Dict, Any
import os

logger = logging.getLogger(__name__)

class SchemaRAGInitializer:
    """Schema RAG系统初始化器"""
    
    def __init__(self):
        self.initialized = False
    
    async def initialize_schema_rag(self, force_rebuild: bool = False) -> Dict[str, Any]:
        """
        初始化Schema RAG系统
        
        Args:
            force_rebuild: 是否强制重建索引
            
        Returns:
            初始化结果信息
        """
        try:
            from .schema_embedding_service import get_schema_rag_orchestrator
            
            logger.info("Starting Schema RAG initialization...")
            
            orchestrator = get_schema_rag_orchestrator()
            await orchestrator.initialize()
            
            # 检查是否需要重建索引
            index_path = "./data/chroma_db"
            needs_rebuild = force_rebuild
            
            if not needs_rebuild and os.path.exists(index_path):
                # 检查是否有索引文件
                from chromadb import PersistentClient
                try:
                    client = PersistentClient(path=index_path)
                    collections = client.list_collections()
                    has_schema_collection = any(c.name == "table_schemas_v2" for c in collections)
                    if not has_schema_collection:
                        needs_rebuild = True
                        logger.info("Schema collection not found, will rebuild")
                except Exception:
                    needs_rebuild = True
                    logger.info("Error checking existing index, will rebuild")
            else:
                needs_rebuild = True
                logger.info("Index directory not found, will create new index")
            
            if needs_rebuild:
                logger.info("Rebuilding schema index...")
                indexed_count = await orchestrator.rebuild_index()
                logger.info(f"Successfully indexed {indexed_count} tables")
            else:
                logger.info("Schema index already exists, skipping rebuild")
                indexed_count = 0
            
            self.initialized = True
            
            return {
                "status": "success",
                "indexed_tables": indexed_count,
                "message": f"Schema RAG system initialized with {indexed_count} tables"
            }
            
        except Exception as e:
            logger.error(f"Schema RAG initialization failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "indexed_tables": 0
            }
    
    async def update_schema_index(self) -> Dict[str, Any]:
        """更新schema索引"""
        try:
            from .schema_embedding_service import get_schema_rag_orchestrator
            
            orchestrator = get_schema_rag_orchestrator()
            await orchestrator.initialize()
            
            indexed_count = await orchestrator.rebuild_index()
            
            logger.info(f"Schema index updated with {indexed_count} tables")
            return {
                "status": "success",
                "indexed_tables": indexed_count,
                "message": f"Schema index updated successfully"
            }
            
        except Exception as e:
            logger.error(f"Schema index update failed: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self.initialized

# 全局初始化器实例
schema_initializer = SchemaRAGInitializer()

async def initialize_schema_system():
    """异步初始化schema系统"""
    return await schema_initializer.initialize_schema_rag()

def sync_initialize_schema_system():
    """同步初始化schema系统（用于启动脚本）"""
    try:
        result = asyncio.run(schema_initializer.initialize_schema_rag())
        return result
    except Exception as e:
        logger.error(f"Failed to initialize schema system: {e}")
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    # 命令行初始化
    import logging
    logging.basicConfig(level=logging.INFO)
    
    result = sync_initialize_schema_system()
    print(f"Initialization result: {result}")