"""
Schema文本生成器 - 采用策略模式优化文本生成逻辑
"""

from typing import Dict, Any, List
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class TextGenerationStrategy(ABC):
    """文本生成策略基类"""
    
    @abstractmethod
    def generate(self, document: Dict[str, Any]) -> str:
        pass
    
    @abstractmethod
    def get_content_type(self) -> str:
        pass

class TableOverviewStrategy(TextGenerationStrategy):
    """表概览文本生成策略"""
    
    def generate(self, document: Dict[str, Any]) -> str:
        table_name = document['table_name']
        description = document.get('description', '')
        business_context = document.get('business_context', '')
        fields_count = len(document.get('fields', []))
        relations_count = len(document.get('relationships', []))
        
        return f"""
表名: {table_name}
描述: {description}
业务场景: {business_context}
字段数量: {fields_count}
关联表数量: {relations_count}
核心用途: {self._extract_core_usage(business_context)}
""".strip()
    
    def get_content_type(self) -> str:
        return "table_overview"
    
    def _extract_core_usage(self, business_context: str) -> str:
        """提取核心用途"""
        if not business_context:
            return "通用数据存储"
        
        # 简化描述，提取前20个字符
        return business_context[:50] + "..." if len(business_context) > 50 else business_context

class FieldsSummaryStrategy(TextGenerationStrategy):
    """字段摘要文本生成策略"""
    
    def generate(self, document: Dict[str, Any]) -> str:
        table_name = document['table_name']
        fields = document.get('fields', [])[:5]  # 限制为前5个重要字段
        
        if not fields:
            return f"{table_name}表无字段信息"
        
        field_summaries = []
        for field in fields:
            if field.get('is_primary_key'):
                field_summaries.append(f"主键:{field['field_name']}({field['data_type']})")
            elif field.get('is_foreign_key'):
                field_summaries.append(f"外键:{field['field_name']}→{field.get('foreign_key_to', '')}")
            else:
                field_summaries.append(f"{field['field_name']}({field['data_type']}):{field.get('description', '')[:20]}")
        
        return f"{table_name}表字段: {'; '.join(field_summaries)}"
    
    def get_content_type(self) -> str:
        return "fields_summary"

class RelationshipsStrategy(TextGenerationStrategy):
    """关系描述文本生成策略"""
    
    def generate(self, document: Dict[str, Any]) -> str:
        table_name = document['table_name']
        relationships = document.get('relationships', [])
        
        if not relationships:
            return f"{table_name}表为独立表，无关联关系"
        
        rel_descriptions = []
        for rel in relationships[:3]:  # 限制为前3个关系
            rel_type = rel.get('type', '关联')
            target = rel.get('target_table', '')
            desc = rel.get('description', '')
            rel_descriptions.append(f"{rel_type}:{target}({desc[:30]}...)")
        
        return f"{table_name}关联: {'; '.join(rel_descriptions)}"
    
    def get_content_type(self) -> str:
        return "relationships"

class SchemaTextGenerator:
    """Schema文本生成器 - 使用策略模式"""
    
    def __init__(self):
        self.strategies = [
            TableOverviewStrategy(),
            FieldsSummaryStrategy(),
            RelationshipsStrategy()
        ]
    
    def generate_all_texts(self, document: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成所有文本变体"""
        results = []
        
        for strategy in self.strategies:
            text = strategy.generate(document)
            content_type = strategy.get_content_type()
            
            results.append({
                'text': text,
                'content_type': content_type,
                'table_name': document['table_name']
            })
        
        return results
    
    def generate_optimized_text(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """生成优化的单一文本（减少向量数量）"""
        table_name = document['table_name']
        description = document.get('description', '')
        business_context = document.get('business_context', '')
        fields = document.get('fields', [])
        relationships = document.get('relationships', [])
        
        # 合并所有信息到一个优化文本
        field_names = [f"{f['field_name']}({f['data_type']})" for f in fields[:3]]
        
        rel_info = []
        for rel in relationships[:2]:
            rel_info.append(f"{rel['target_table']}({rel['type']})")
        
        optimized_text = f"""
表:{table_name}
描述:{description}
业务:{business_context}
字段:{', '.join(field_names)}{'...' if len(fields) > 3 else ''}
关联:{'; '.join(rel_info) if rel_info else '无'}
""".strip()
        
        return {
            'text': optimized_text,
            'content_type': 'optimized_summary',
            'table_name': table_name
        }