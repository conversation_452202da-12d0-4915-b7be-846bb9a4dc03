#!/usr/bin/env python3
"""
异步数据库功能测试脚本

验证数据库异步化改造后的功能正确性、性能提升和兼容性保证。
"""

import sys
import os
import asyncio
import time
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.services.database import async_db_manager, db
    from app.services.query_service import run_sql, run_sql_sync, DateTimeEncoder
    from app.services.db_session import get_async_db_manager
    
    print("=== 异步数据库功能测试 ===")
    
    async def test_async_connection():
        """测试异步数据库连接"""
        print("\n--- 测试异步数据库连接 ---")
        
        try:
            is_connected = await async_db_manager.test_connection()
            if is_connected:
                print("✓ 异步数据库连接成功")
                return True
            else:
                print("✗ 异步数据库连接失败")
                return False
        except Exception as e:
            print(f"✗ 异步数据库连接测试异常: {e}")
            return False
    
    async def test_basic_query():
        """测试基本异步查询功能"""
        print("\n--- 测试基本异步查询功能 ---")
        
        test_sql = "SELECT COUNT(*) as total_experts FROM erp_expert LIMIT 1"
        
        try:
            result = await run_sql(test_sql)
            print(f"查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 验证结果格式
            if "data" in result and "columns" in result:
                print("✓ 异步查询结果格式正确")
                
                if result["data"] and len(result["data"]) > 0:
                    print("✓ 异步查询返回数据正常")
                    return True
                else:
                    print("⚠ 异步查询未返回数据（可能是数据库为空）")
                    return True
            else:
                print("✗ 异步查询结果格式错误")
                return False
                
        except Exception as e:
            print(f"✗ 异步查询测试异常: {e}")
            return False
    
    async def test_concurrent_queries():
        """测试并发查询性能"""
        print("\n--- 测试并发查询性能 ---")
        
        test_queries = [
            "SELECT COUNT(*) as count FROM erp_expert LIMIT 1",
            "SELECT COUNT(*) as count FROM erp_expert WHERE sex = 1 LIMIT 1",
            "SELECT COUNT(*) as count FROM erp_expert WHERE sex = 2 LIMIT 1",
            "SELECT COUNT(*) as count FROM erp_expert WHERE professional LIKE '%教授%' LIMIT 1",
            "SELECT COUNT(*) as count FROM erp_expert WHERE professional LIKE '%研究员%' LIMIT 1"
        ]
        
        try:
            # 测试并发执行
            start_time = time.time()
            
            tasks = [run_sql(sql) for sql in test_queries]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            concurrent_time = end_time - start_time
            
            print(f"并发执行 {len(test_queries)} 个查询耗时: {concurrent_time:.3f} 秒")
            
            # 验证所有查询都成功
            success_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"  查询 {i+1} 失败: {result}")
                elif isinstance(result, dict) and "data" in result:
                    success_count += 1
                    print(f"  查询 {i+1} 成功")
                else:
                    print(f"  查询 {i+1} 结果格式异常")
            
            if success_count == len(test_queries):
                print(f"✓ 所有 {len(test_queries)} 个并发查询都成功执行")
                return True, concurrent_time
            else:
                print(f"✗ 只有 {success_count}/{len(test_queries)} 个查询成功")
                return False, concurrent_time
                
        except Exception as e:
            print(f"✗ 并发查询测试异常: {e}")
            return False, 0
    
    async def test_error_handling():
        """测试错误处理机制"""
        print("\n--- 测试错误处理机制 ---")
        
        # 故意使用错误的SQL语句
        invalid_sql = "SELECT * FROM non_existent_table"
        
        try:
            result = await run_sql(invalid_sql)
            print(f"错误查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            # 验证错误处理格式
            if (result.get("data") and 
                len(result["data"]) > 0 and 
                result["data"][0][0] == "SQL_EXECUTION_ERROR"):
                print("✓ 异步查询错误处理格式正确")
                return True
            else:
                print("✗ 异步查询错误处理格式不正确")
                return False
                
        except Exception as e:
            print(f"✗ 错误处理测试异常: {e}")
            return False
    
    def test_langchain_compatibility():
        """测试LangChain兼容性"""
        print("\n--- 测试LangChain兼容性 ---")
        
        try:
            # 测试LangChain SQLDatabase是否正常工作
            table_names = db.get_usable_table_names()
            print(f"LangChain可用表数量: {len(table_names)}")
            
            if table_names and len(table_names) > 0:
                print("✓ LangChain SQLDatabase功能正常")
                print(f"  主要表: {table_names[:5]}")  # 显示前5个表
                return True
            else:
                print("✗ LangChain SQLDatabase无法获取表信息")
                return False
                
        except Exception as e:
            print(f"✗ LangChain兼容性测试异常: {e}")
            return False
    
    async def run_all_tests():
        """运行所有测试"""
        print("开始异步数据库功能测试...\n")
        
        test_results = []
        
        # 1. 连接测试
        connection_ok = await test_async_connection()
        test_results.append(("异步连接", connection_ok))
        
        if not connection_ok:
            print("\n❌ 异步数据库连接失败，跳过后续测试")
            return
        
        # 2. 基本查询测试
        basic_query_ok = await test_basic_query()
        test_results.append(("基本查询", basic_query_ok))
        
        # 3. 并发查询测试
        concurrent_ok, concurrent_time = await test_concurrent_queries()
        test_results.append(("并发查询", concurrent_ok))
        
        # 4. 错误处理测试
        error_handling_ok = await test_error_handling()
        test_results.append(("错误处理", error_handling_ok))
        
        # 5. LangChain兼容性测试
        langchain_ok = test_langchain_compatibility()
        test_results.append(("LangChain兼容性", langchain_ok))
        
        # 汇总结果
        print("\n=== 测试结果汇总 ===")
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有异步数据库功能测试通过！")
            print("✅ 数据库异步化改造成功")
        else:
            print("⚠️ 部分测试失败，需要检查配置或代码")
        
        return passed == total
    
    if __name__ == "__main__":
        # 运行异步测试
        asyncio.run(run_all_tests())

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有必要的依赖包，特别是aiomysql")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
