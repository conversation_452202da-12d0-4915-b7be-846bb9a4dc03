#!/usr/bin/env python3
"""
基础迁移测试脚本

简单验证FastAPI应用是否能正常启动和响应基本请求。
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from fastapi.testclient import TestClient
    from app import create_app
    
    print("=== FastAPI迁移基础测试 ===")
    
    # 创建测试客户端
    app = create_app()
    client = TestClient(app)
    
    print("✓ FastAPI应用创建成功")
    
    # 测试建议接口
    print("\n--- 测试建议接口 ---")
    response = client.post("/api/suggestions", json={"query": "按学历"})
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print("✓ 建议接口工作正常")
    else:
        print(f"✗ 建议接口错误: {response.text}")
    
    # 测试API文档
    print("\n--- 测试API文档 ---")
    docs_response = client.get("/docs")
    print(f"Swagger UI状态码: {docs_response.status_code}")
    if docs_response.status_code == 200:
        print("✓ API文档可访问")
    
    openapi_response = client.get("/openapi.json")
    print(f"OpenAPI规范状态码: {openapi_response.status_code}")
    if openapi_response.status_code == 200:
        openapi_spec = openapi_response.json()
        print(f"API端点数量: {len(openapi_spec.get('paths', {}))}")
        print("✓ OpenAPI规范生成正常")
    
    # 测试查询接口（基本结构）
    print("\n--- 测试查询接口基本结构 ---")
    query_response = client.post("/api/query", json={"question": "测试问题"})
    print(f"查询接口状态码: {query_response.status_code}")
    print(f"响应头Content-Type: {query_response.headers.get('content-type')}")
    
    if query_response.status_code == 200:
        print("✓ 查询接口基本结构正常")
        # 注意：实际的流式内容可能因为LLM API配置而异
    else:
        print(f"查询接口响应: {query_response.text}")
    
    print("\n=== 基础测试完成 ===")
    print("✓ FastAPI迁移基本成功")
    print("✓ 所有核心接口结构正确")
    print("✓ API文档自动生成正常")
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有必要的依赖包")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
