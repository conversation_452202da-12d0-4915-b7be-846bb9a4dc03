#!/usr/bin/env python3
"""
ChatBI系统压力测试主脚本

整合所有组件的主测试脚本，包括测试流程编排、用户交互界面、
结果汇总和分析、异常处理和恢复。
"""

import asyncio
import time
import json
import logging
import argparse
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime

# 导入自定义模块
from stress_test_framework import StressTestFramework, TestConfig, PerformanceMetrics
from streaming_test_handler import StreamingTestHandler
from performance_monitor import RealTimeMonitor, AlertManager, PerformanceAnalyzer
from test_scenarios import TestScenarios, QueryComplexity
from report_generator import ReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stress_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ChatBIStressTest:
    """ChatBI压力测试主类"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.scenarios = TestScenarios()
        self.report_generator = ReportGenerator()
        self.test_results = {
            'start_time': None,
            'end_time': None,
            'config': config.__dict__,
            'step_results': [],
            'summary': {}
        }
        
        # 性能监控组件
        self.real_time_monitor = RealTimeMonitor()
        self.alert_manager = AlertManager()
        self.performance_analyzer = PerformanceAnalyzer()
        
        # 设置告警规则
        self._setup_alert_rules()
    
    def _setup_alert_rules(self):
        """设置性能告警规则"""
        self.alert_manager.add_alert_rule(
            "high_response_time",
            lambda s: s.avg_response_time > 10,
            "平均响应时间超过10秒",
            cooldown_seconds=30
        )
        
        self.alert_manager.add_alert_rule(
            "low_success_rate",
            lambda s: s.success_rate < 0.9,
            "成功率低于90%",
            cooldown_seconds=60
        )
        
        self.alert_manager.add_alert_rule(
            "high_cpu_usage",
            lambda s: s.cpu_percent > 90,
            "CPU使用率超过90%",
            cooldown_seconds=45
        )
        
        self.alert_manager.add_alert_rule(
            "high_memory_usage",
            lambda s: s.memory_percent > 90,
            "内存使用率超过90%",
            cooldown_seconds=45
        )
    
    async def run_stress_test(self) -> Dict[str, Any]:
        """运行完整的压力测试"""
        logger.info("开始ChatBI系统压力测试")
        self.test_results['start_time'] = datetime.now().isoformat()
        
        try:
            # 启动实时监控
            await self._start_monitoring()
            
            # 执行渐进式压力测试
            await self._run_progressive_test()
            
            # 生成测试报告
            await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"压力测试过程中发生错误: {e}")
            raise
        finally:
            # 停止监控
            await self._stop_monitoring()
            self.test_results['end_time'] = datetime.now().isoformat()
        
        return self.test_results
    
    async def _start_monitoring(self):
        """启动性能监控"""
        # 添加告警检查回调
        self.real_time_monitor.add_callback(self.alert_manager.check_alerts)
        
        # 这里需要传入performance_collector，但由于架构限制，暂时跳过
        # await self.real_time_monitor.start_monitoring(performance_collector)
        logger.info("性能监控已启动")
    
    async def _stop_monitoring(self):
        """停止性能监控"""
        await self.real_time_monitor.stop_monitoring()
        logger.info("性能监控已停止")
    
    async def _run_progressive_test(self):
        """运行渐进式压力测试"""
        logger.info("开始渐进式压力测试")
        
        async with StressTestFramework(self.config) as framework:
            for concurrent_users in self.config.ramp_up_steps:
                logger.info(f"测试并发用户数: {concurrent_users}")
                
                # 检查是否应该停止测试
                if await self._should_stop_test(concurrent_users):
                    logger.warning(f"在{concurrent_users}并发用户时停止测试")
                    break
                
                # 执行单步测试
                step_result = await self._run_single_step(framework, concurrent_users)
                self.test_results['step_results'].append(step_result)
                
                # 分析结果并决定是否继续
                if not self._should_continue_test(step_result):
                    logger.warning("根据性能指标决定停止测试")
                    break
                
                # 步骤间休息
                await asyncio.sleep(5)
    
    async def _should_stop_test(self, concurrent_users: int) -> bool:
        """判断是否应该停止测试"""
        # 检查API限流预估
        expected_load = self.scenarios.calculate_expected_load(
            concurrent_users, 
            self.config.test_duration_minutes
        )
        
        if expected_load['estimated_qpm'] > self.config.qpm_limit:
            logger.warning(f"预估QPM ({expected_load['estimated_qpm']}) 超过限制")
            return True
        
        if expected_load['estimated_tpm'] > self.config.tpm_limit:
            logger.warning(f"预估TPM ({expected_load['estimated_tpm']}) 超过限制")
            return True
        
        return False
    
    def _should_continue_test(self, step_result: Dict[str, Any]) -> bool:
        """判断是否应该继续测试"""
        stats = step_result.get('performance_stats', {})
        
        # 成功率过低
        if stats.get('success_rate', 0) < 0.8:
            return False
        
        # 响应时间过长
        if stats.get('avg_response_time', 0) > 15:
            return False
        
        # 系统资源过载
        system_stats = step_result.get('system_stats', {})
        if system_stats.get('avg_cpu_percent', 0) > 95:
            return False
        
        return True
    
    async def _run_single_step(self, framework: StressTestFramework, 
                             concurrent_users: int) -> Dict[str, Any]:
        """运行单个测试步骤"""
        logger.info(f"开始测试 {concurrent_users} 并发用户")
        
        step_start_time = time.time()
        
        # 创建混合工作负载
        workload = self.scenarios.create_mixed_workload(concurrent_users)
        
        # 执行并发测试
        tasks = []
        for query, pattern_name in workload:
            if query.question.startswith("统计") or query.question.startswith("查询"):
                # 流式查询测试
                task = self._test_streaming_query(framework, query)
            else:
                # 建议查询测试
                task = self._test_suggestion_query(framework, query)
            
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集性能指标
        successful_results = [r for r in results if isinstance(r, PerformanceMetrics) and r.success]
        failed_results = [r for r in results if isinstance(r, PerformanceMetrics) and not r.success]
        exception_results = [r for r in results if isinstance(r, Exception)]
        
        step_duration = time.time() - step_start_time
        
        # 计算统计信息
        performance_stats = self._calculate_step_statistics(
            successful_results, failed_results, exception_results, step_duration
        )
        
        # 获取系统资源统计
        system_stats = await self._get_system_statistics()
        
        # 获取API限流状态
        rate_limit_status = await framework.get_rate_limit_status()
        
        step_result = {
            'concurrent_users': concurrent_users,
            'duration': step_duration,
            'performance_stats': performance_stats,
            'system_stats': system_stats,
            'rate_limit_status': rate_limit_status,
            'workload_distribution': self._analyze_workload_distribution(workload),
            'raw_response_times': [r.response_time for r in successful_results]
        }
        
        logger.info(f"完成 {concurrent_users} 并发用户测试: "
                   f"成功率 {performance_stats['success_rate']:.1%}, "
                   f"平均响应时间 {performance_stats['avg_response_time']:.2f}s")
        
        return step_result
    
    async def _test_streaming_query(self, framework: StressTestFramework, 
                                  query) -> PerformanceMetrics:
        """测试流式查询"""
        return await framework.make_request(
            "POST", 
            "/api/query", 
            {"question": query.question},
            concurrent_users=1,  # 这里需要传入实际的并发数
            estimated_tokens=query.estimated_tokens
        )
    
    async def _test_suggestion_query(self, framework: StressTestFramework, 
                                   query) -> PerformanceMetrics:
        """测试建议查询"""
        # 使用查询的前几个字符作为建议查询
        suggestion_prefix = query.question[:3]
        
        return await framework.make_request(
            "POST",
            "/api/suggestions",
            {"query": suggestion_prefix},
            concurrent_users=1,  # 这里需要传入实际的并发数
            estimated_tokens=20  # 建议查询消耗较少Token
        )
    
    def _calculate_step_statistics(self, successful_results: List[PerformanceMetrics],
                                 failed_results: List[PerformanceMetrics],
                                 exception_results: List[Exception],
                                 step_duration: float) -> Dict[str, Any]:
        """计算步骤统计信息"""
        total_requests = len(successful_results) + len(failed_results) + len(exception_results)
        successful_requests = len(successful_results)
        
        if not successful_results:
            return {
                'total_requests': total_requests,
                'successful_requests': 0,
                'failed_requests': total_requests,
                'success_rate': 0,
                'avg_response_time': 0,
                'min_response_time': 0,
                'max_response_time': 0,
                'p95_response_time': 0,
                'p99_response_time': 0,
                'qps': 0
            }
        
        response_times = [r.response_time for r in successful_results]
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': total_requests - successful_requests,
            'success_rate': successful_requests / total_requests,
            'avg_response_time': sum(response_times) / len(response_times),
            'min_response_time': min(response_times),
            'max_response_time': max(response_times),
            'p95_response_time': self._percentile(response_times, 95),
            'p99_response_time': self._percentile(response_times, 99),
            'qps': successful_requests / step_duration
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    async def _get_system_statistics(self) -> Dict[str, Any]:
        """获取系统资源统计"""
        import psutil
        
        return {
            'avg_cpu_percent': psutil.cpu_percent(interval=1),
            'avg_memory_percent': psutil.virtual_memory().percent,
            'memory_used_mb': psutil.virtual_memory().used / 1024 / 1024
        }
    
    def _analyze_workload_distribution(self, workload) -> Dict[str, Any]:
        """分析工作负载分布"""
        complexity_count = {}
        pattern_count = {}
        
        for query, pattern_name in workload:
            complexity = query.complexity.value
            complexity_count[complexity] = complexity_count.get(complexity, 0) + 1
            pattern_count[pattern_name] = pattern_count.get(pattern_name, 0) + 1
        
        return {
            'complexity_distribution': complexity_count,
            'pattern_distribution': pattern_count,
            'total_queries': len(workload)
        }
    
    async def _generate_final_report(self):
        """生成最终测试报告"""
        logger.info("生成测试报告")
        
        # 生成性能图表
        charts = self.report_generator.generate_performance_charts(self.test_results)
        
        # 生成HTML报告
        html_report = self.report_generator.generate_html_report(self.test_results, charts)
        
        # 保存报告
        report_path = self.report_generator.save_report(html_report)
        data_path = self.report_generator.export_raw_data(self.test_results)
        
        logger.info(f"HTML报告已保存: {report_path}")
        logger.info(f"原始数据已导出: {data_path}")
        
        # 更新测试结果
        self.test_results['report_path'] = report_path
        self.test_results['data_path'] = data_path


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ChatBI系统压力测试')
    parser.add_argument('--base-url', default='http://localhost:5000', help='测试目标URL')
    parser.add_argument('--max-users', type=int, default=1000, help='最大并发用户数')
    parser.add_argument('--duration', type=int, default=2, help='每步测试持续时间(分钟)')
    parser.add_argument('--qpm-limit', type=int, default=1200, help='QPM限制')
    parser.add_argument('--tpm-limit', type=int, default=5000000, help='TPM限制')
    
    args = parser.parse_args()
    
    # 创建测试配置
    config = TestConfig(
        base_url=args.base_url,
        max_concurrent_users=args.max_users,
        test_duration_minutes=args.duration,
        qpm_limit=args.qpm_limit,
        tpm_limit=args.tpm_limit
    )
    
    # 创建并运行测试
    stress_test = ChatBIStressTest(config)
    
    try:
        results = await stress_test.run_stress_test()
        
        print("\n" + "="*60)
        print("压力测试完成!")
        print(f"报告路径: {results.get('report_path', 'N/A')}")
        print(f"数据路径: {results.get('data_path', 'N/A')}")
        print("="*60)
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
