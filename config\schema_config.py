"""
Schema配置管理 - 将硬编码配置移出代码
"""

from typing import Dict, List, Any
import json
import os

class SchemaConfig:
    """Schema配置管理器"""
    
    # 目标表配置
    TARGET_TABLES = [
        'erp_expert', 'erp_expert_info', 'erp_expert_apply', 'erp_expert_dev',
        'erp_expert_work', 'erp_expert_resume', 'erp_expert_favorite', 
        'erp_expert_log', 'erp_expert_search', 'erp_expert_support', 'erp_expert_tag'
    ]
    
    # 表描述配置
    TABLE_DESCRIPTIONS = {
        "erp_expert": "专家主信息表，存储专家的基本信息和核心属性",
        "erp_expert_info": "专家详细信息表，包含教育背景、工作经历等扩展信息",
        "erp_expert_apply": "专家申请记录表，记录所有专家合作申请历史",
        "erp_expert_dev": "专家开发记录表，跟踪专家的开发状态和进展",
        "erp_expert_work": "专家工作经历表，记录专家的职位履历",
        "erp_expert_resume": "专家简历信息表，存储详细的学术和职业背景",
        "erp_expert_favorite": "专家收藏记录表，记录用户对专家的收藏行为",
        "erp_expert_log": "专家日志记录表，记录用户对专家的操作日志",
        "erp_expert_search": "专家搜索记录表，记录用户对专家的搜索行为",
        "erp_expert_support": "专家支持记录表，记录用户对专家的支持行为",
        "erp_expert_tag": "专家标签记录表，记录用户对专家的标签行为"
    }
    
    # 业务上下文配置
    BUSINESS_CONTEXTS = {
        "erp_expert": "所有专家相关业务的入口表，其他专家表都通过expertId关联到此表",
        "erp_expert_info": "专家信息的扩展存储，与主表一对一关系，提供详细信息",
        "erp_expert_apply": "核心业务表，记录专家从申请到合作的完整生命周期",
        "erp_expert_dev": "专家开发过程跟踪，用于分析开发效率和成功率",
        "erp_expert_work": "专家职业履历分析，用于评估专家经验和能力",
        "erp_expert_resume": "专家简历信息表，存储详细的学术和职业背景",
        "erp_expert_favorite": "专家收藏记录表，记录用户对专家的收藏行为",
        "erp_expert_log": "专家日志记录表，记录用户对专家的操作日志",
        "erp_expert_search": "专家搜索记录表，记录用户对专家的搜索行为",
        "erp_expert_support": "专家支持记录表，记录用户对专家的支持行为",
        "erp_expert_tag": "专家标签记录表，记录用户对专家的标签行为"
    }
    
    # 字段枚举值配置
    FIELD_ENUMS = {
        "erp_expert": {
            "status": {
                "1": "未开发", "2": "待启动", "3": "开发中", "4": "审核中",
                "5": "开发成功", "6": "开发失败", "7": "开发异常", "8": "待补充"
            },
            "sex": {"1": "男", "2": "女"},
            "professional": {
                "1": "教授", "2": "副教授", "3": "助理教授", "4": "研究员",
                "5": "副研究员", "6": "助理研究员", "7": "正高级工程师", "8": "高级工程师",
                "9": "讲师", "10": "学生"
            },
        
            "domain": {
                "1": "环境及资源科学技术", "2": "能源科学", "3": "化学", "4": "计算机科学",
                "5": "通信与电子工程", "6": "数学", "7": "物理学", "8": "材料科学",
                "9": "土木与建筑工程", "10": "交通运输工程", "11": "机械工程", "12": "电气工程",
                "13": "控制科学与工程", "14": "仪器科学与技术", "15": "化学工程与技术",
                "16": "矿业工程", "17": "石油与天然气工程", "18": "纺织科学与工程",
                "19": "水利工程", "20": "其他学科", "21": "环境科学", "22": "心理学",
                "23": "语言教育艺术体育", "24": "经济学", "25": "管理学", "26": "动力与电气工程",
                "27": "力学与物理学", "28": "测绘科学", "29": "航空航天科学", "30": "纺织科学",
                "31": "安全科学", "32": "农林畜牧水产", "33": "机械工程", "34": "冶金工程",
                "35": "生物学与生物工程", "36": "地理科学", "37": "天文学", "38": "民族宗教",
                "39": "医学与药学", "40": "核科学", "41": "文学历史哲学", "42": "政治学",
                "43": "法学", "44": "海洋科学", "45": "光学", "46": "教育学"
            },
            "pf": {
                "1": "艾思专家", "2": "智库专家", "3": "论文作者",
                "4": "导师数据库", "5": "专家总库新增/导入"
            },
            "purpose": {
                "1": "学术合作", "2": "项目合作", "3": "技术咨询",
                "4": "成果转化", "5": "人才培养"
            }
        }
    }
    
    # 位运算字段配置
    BITWISE_FIELDS = {
        "erp_expert": {
            "officer": {
                "4": "审稿专家", "8": "编译专家", "16": "课程导师",
                "32": "会议嘉宾", "64": "头条创作者"
            },
            "title": {
                "1": "院士", "2": "国家级高层次人才", "4": "国家级青年人才",
                "8": "IEEE Fellow", "16": "ACM Fellow", "32": "IEEE高级会员", "64": "ACM高级会员"
            }
        }
    }
    
    # 业务规则配置
    BUSINESS_RULES = {
        "erp_expert": {
            "status": "只有status=5的开发成功专家才能被推荐给客户合作",
            "officer": "使用位运算&进行筛选，如(officer & 4) = 4表示审稿专家",
            "title": "院士(title & 1 = 1)为最高级别专家，优先推荐",
            "createTime": "按时间分析专家增长趋势，用于业务决策"
        }
    }
    
    # 预定义关系配置
    PREDEFINED_RELATIONSHIPS = {
        "erp_expert": [
            {
                "target_table": "erp_expert_info",
                "type": "one_to_one",
                "join_condition": "erp_expert.id = erp_expert_info.expertId",
                "description": "专家详细信息扩展，一对一关系"
            },
            {
                "target_table": "erp_expert_apply",
                "type": "one_to_many",
                "join_condition": "erp_expert.id = erp_expert_apply.expertId",
                "description": "专家申请记录，一个专家多条申请"
            },
            {
                "target_table": "erp_expert_dev",
                "type": "one_to_many",
                "join_condition": "erp_expert.id = erp_expert_dev.expertId",
                "description": "专家开发记录，跟踪开发过程"
            },
            {
                "target_table": "erp_expert_work",
                "type": "one_to_many",
                "join_condition": "erp_expert.id = erp_expert_work.expertId",
                "description": "专家工作经历，记录职业履历"
            }
        ]
    }
    
    # 嵌入配置
    EMBEDDING_CONFIG = {
        "model": "text-embedding-v4",
        "dimension": 1024,
        "batch_size": 10,
        "max_tokens": 8000,
        "collection_name": "table_schemas_v3"
    }
    
    @classmethod
    def load_from_json(cls, config_path: str) -> Dict[str, Any]:
        """从JSON文件加载配置"""
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    @classmethod
    def save_to_json(cls, config: Dict[str, Any], config_path: str):
        """保存配置到JSON文件"""
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)