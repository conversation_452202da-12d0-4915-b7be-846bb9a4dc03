#!/usr/bin/env python3
"""
Schema RAG系统初始化脚本
单独运行，避免相对导入问题
"""

import os
import sys
import asyncio
import json
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_config():
    """获取数据库配置"""
    from app.services.database import load_env
    load_env()
    
    return {
        'host': os.getenv('DB_HOST', 'dev.ais.cn'),
        'user': os.getenv('DB_USER', 'erp_select'),
        'password': os.getenv('DB_PASSWORD', 'erp_select'),
        'database': os.getenv('DB_NAME', 'scholar_erp'),
        'port': int(os.getenv('DB_PORT', 3306))
    }

def setup_chroma_db():
    """设置ChromaDB"""
    import chromadb
    from chromadb.config import Settings
    
    persist_directory = "./data/chroma_db"
    os.makedirs(persist_directory, exist_ok=True)
    
    client = chromadb.PersistentClient(
        path=persist_directory,
        settings=Settings(anonymized_telemetry=False)
    )
    
    return client

async def rebuild_schema_index():
    """重建schema索引"""
    try:
        from app.services.schema_embedding_service import get_schema_rag_orchestrator
        
        logger.info("开始重建schema索引...")
        
        # 使用新的SchemaRAGOrchestrator系统
        orchestrator = get_schema_rag_orchestrator()
        await orchestrator.initialize()
        
        # 重建索引
        indexed_count = await orchestrator.rebuild_index()
        
        logger.info(f"索引重建完成，共索引了 {indexed_count} 个表")
        return indexed_count
        
    except Exception as e:
        logger.error(f"重建索引失败: {str(e)}")
        raise

async def test_schema_search():
    """测试schema搜索功能"""
    try:
        from app.services.schema_embedding_service import get_schema_rag_orchestrator
        
        orchestrator = get_schema_rag_orchestrator()
        await orchestrator.initialize()
        
        # 测试搜索
        test_queries = [
            "查找开发成功的专家",
            "统计专家数量",
            "专家状态分布"
        ]
        
        for query in test_queries:
            results = await orchestrator.get_context_for_query(query)
            print(f"\n查询: {query}")
            print(f"找到相关表: {results.get('primary_tables', [])}")
    
    except Exception as e:
        logger.error(f"测试搜索失败: {str(e)}")

def main():
    """主函数"""
    logger.info("=== Schema RAG系统初始化工具 ===")
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        logger.error("未设置DASHSCOPE_API_KEY环境变量")
        print("请设置环境变量: export DASHSCOPE_API_KEY=your_key_here")
        return 1
    
    try:
        # 重建索引
        indexed_count = asyncio.run(rebuild_schema_index())
        
        # 测试搜索
        asyncio.run(test_schema_search())
        
        print(f"\n✅ 初始化完成！共索引了 {indexed_count} 个表")
        return 0
        
    except Exception as e:
        logger.error(f"初始化失败: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())