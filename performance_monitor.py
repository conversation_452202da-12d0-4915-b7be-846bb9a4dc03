#!/usr/bin/env python3
"""
性能监控系统

实现全面的性能监控，包括响应时间统计、系统资源监控、
数据库连接池状态和实时性能指标展示。
"""

import asyncio
import psutil
import time
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import statistics
from collections import defaultdict, deque
import threading

logger = logging.getLogger(__name__)


@dataclass
class PerformanceSnapshot:
    """性能快照"""
    timestamp: float
    qps: float
    avg_response_time: float
    p95_response_time: float
    success_rate: float
    active_connections: int
    cpu_percent: float
    memory_percent: float
    error_rate: float


@dataclass
class DatabaseMetrics:
    """数据库性能指标"""
    timestamp: float
    active_connections: int = 0
    idle_connections: int = 0
    total_connections: int = 0
    connection_pool_size: int = 0
    overflow_connections: int = 0
    avg_query_time: float = 0
    slow_queries: int = 0


class RealTimeMonitor:
    """实时性能监控器"""
    
    def __init__(self, update_interval: float = 1.0):
        self.update_interval = update_interval
        self.snapshots: deque = deque(maxlen=300)  # 保留5分钟的数据
        self.callbacks: List[Callable] = []
        self.monitoring = False
        self.monitor_task = None
        self.lock = asyncio.Lock()
        
        # 性能数据缓存
        self.recent_metrics = deque(maxlen=100)
        self.recent_system_metrics = deque(maxlen=100)
    
    def add_callback(self, callback: Callable[[PerformanceSnapshot], None]):
        """添加性能更新回调"""
        self.callbacks.append(callback)
    
    async def start_monitoring(self, performance_collector):
        """开始实时监控"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(
            self._monitor_loop(performance_collector)
        )
    
    async def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_loop(self, performance_collector):
        """监控循环"""
        while self.monitoring:
            try:
                # 生成性能快照
                snapshot = await self._create_snapshot(performance_collector)
                
                async with self.lock:
                    self.snapshots.append(snapshot)
                
                # 通知回调
                for callback in self.callbacks:
                    try:
                        callback(snapshot)
                    except Exception as e:
                        logger.error(f"监控回调错误: {e}")
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _create_snapshot(self, performance_collector) -> PerformanceSnapshot:
        """创建性能快照"""
        current_time = time.time()
        
        # 获取最近的性能统计
        stats = performance_collector.get_statistics(time_window_seconds=60)
        
        # 获取系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()
        
        return PerformanceSnapshot(
            timestamp=current_time,
            qps=stats.get("qps", 0),
            avg_response_time=stats.get("avg_response_time", 0),
            p95_response_time=stats.get("p95_response_time", 0),
            success_rate=stats.get("success_rate", 0),
            active_connections=0,  # 需要从数据库获取
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            error_rate=1 - stats.get("success_rate", 0)
        )
    
    async def get_recent_snapshots(self, count: int = 60) -> List[PerformanceSnapshot]:
        """获取最近的性能快照"""
        async with self.lock:
            return list(self.snapshots)[-count:]
    
    def get_performance_trend(self, metric_name: str, minutes: int = 5) -> Dict[str, Any]:
        """获取性能趋势"""
        cutoff_time = time.time() - (minutes * 60)
        recent_snapshots = [
            s for s in self.snapshots 
            if s.timestamp >= cutoff_time
        ]
        
        if not recent_snapshots:
            return {}
        
        values = [getattr(s, metric_name, 0) for s in recent_snapshots]
        
        return {
            "current": values[-1] if values else 0,
            "avg": statistics.mean(values) if values else 0,
            "min": min(values) if values else 0,
            "max": max(values) if values else 0,
            "trend": self._calculate_trend(values)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return "stable"
        
        # 简单的趋势计算：比较前半部分和后半部分的平均值
        mid = len(values) // 2
        first_half_avg = statistics.mean(values[:mid])
        second_half_avg = statistics.mean(values[mid:])
        
        if second_half_avg > first_half_avg * 1.1:
            return "increasing"
        elif second_half_avg < first_half_avg * 0.9:
            return "decreasing"
        else:
            return "stable"


class AlertManager:
    """性能告警管理器"""
    
    def __init__(self):
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)
    
    def add_alert_rule(self, name: str, condition: Callable[[PerformanceSnapshot], bool], 
                      message: str, cooldown_seconds: int = 60):
        """添加告警规则"""
        self.alert_rules[name] = {
            "condition": condition,
            "message": message,
            "cooldown": cooldown_seconds,
            "last_triggered": 0
        }
    
    def check_alerts(self, snapshot: PerformanceSnapshot):
        """检查告警条件"""
        current_time = time.time()
        
        for rule_name, rule in self.alert_rules.items():
            # 检查冷却时间
            if current_time - rule["last_triggered"] < rule["cooldown"]:
                continue
            
            # 检查告警条件
            if rule["condition"](snapshot):
                self._trigger_alert(rule_name, rule["message"], snapshot)
                rule["last_triggered"] = current_time
    
    def _trigger_alert(self, rule_name: str, message: str, snapshot: PerformanceSnapshot):
        """触发告警"""
        alert = {
            "rule_name": rule_name,
            "message": message,
            "timestamp": snapshot.timestamp,
            "snapshot": snapshot
        }
        
        self.active_alerts[rule_name] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"性能告警: {rule_name} - {message}")
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def clear_alert(self, rule_name: str):
        """清除告警"""
        if rule_name in self.active_alerts:
            del self.active_alerts[rule_name]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.baseline_metrics = None
    
    def set_baseline(self, snapshots: List[PerformanceSnapshot]):
        """设置性能基线"""
        if not snapshots:
            return
        
        self.baseline_metrics = {
            "avg_response_time": statistics.mean([s.avg_response_time for s in snapshots]),
            "p95_response_time": statistics.mean([s.p95_response_time for s in snapshots]),
            "qps": statistics.mean([s.qps for s in snapshots]),
            "success_rate": statistics.mean([s.success_rate for s in snapshots]),
            "cpu_percent": statistics.mean([s.cpu_percent for s in snapshots]),
            "memory_percent": statistics.mean([s.memory_percent for s in snapshots])
        }
    
    def analyze_performance_degradation(self, current_snapshots: List[PerformanceSnapshot]) -> Dict[str, Any]:
        """分析性能退化"""
        if not self.baseline_metrics or not current_snapshots:
            return {}
        
        current_metrics = {
            "avg_response_time": statistics.mean([s.avg_response_time for s in current_snapshots]),
            "p95_response_time": statistics.mean([s.p95_response_time for s in current_snapshots]),
            "qps": statistics.mean([s.qps for s in current_snapshots]),
            "success_rate": statistics.mean([s.success_rate for s in current_snapshots]),
            "cpu_percent": statistics.mean([s.cpu_percent for s in current_snapshots]),
            "memory_percent": statistics.mean([s.memory_percent for s in current_snapshots])
        }
        
        analysis = {}
        
        for metric, current_value in current_metrics.items():
            baseline_value = self.baseline_metrics[metric]
            
            if baseline_value > 0:
                change_percent = ((current_value - baseline_value) / baseline_value) * 100
                
                analysis[metric] = {
                    "baseline": baseline_value,
                    "current": current_value,
                    "change_percent": change_percent,
                    "degraded": self._is_degraded(metric, change_percent)
                }
        
        return analysis
    
    def _is_degraded(self, metric: str, change_percent: float) -> bool:
        """判断性能是否退化"""
        # 定义退化阈值
        degradation_thresholds = {
            "avg_response_time": 20,  # 响应时间增加20%
            "p95_response_time": 25,  # P95响应时间增加25%
            "qps": -15,  # QPS下降15%
            "success_rate": -5,  # 成功率下降5%
            "cpu_percent": 30,  # CPU使用率增加30%
            "memory_percent": 25  # 内存使用率增加25%
        }
        
        threshold = degradation_thresholds.get(metric, 20)
        
        if metric in ["qps", "success_rate"]:
            # 对于QPS和成功率，负变化表示退化
            return change_percent < threshold
        else:
            # 对于响应时间、CPU、内存，正变化表示退化
            return change_percent > threshold
    
    def identify_bottlenecks(self, snapshots: List[PerformanceSnapshot]) -> List[str]:
        """识别性能瓶颈"""
        if not snapshots:
            return []
        
        bottlenecks = []
        
        # 分析最近的快照
        recent = snapshots[-10:] if len(snapshots) >= 10 else snapshots
        
        avg_cpu = statistics.mean([s.cpu_percent for s in recent])
        avg_memory = statistics.mean([s.memory_percent for s in recent])
        avg_response_time = statistics.mean([s.avg_response_time for s in recent])
        avg_success_rate = statistics.mean([s.success_rate for s in recent])
        
        # CPU瓶颈
        if avg_cpu > 80:
            bottlenecks.append("CPU使用率过高，可能需要优化计算密集型操作")
        
        # 内存瓶颈
        if avg_memory > 85:
            bottlenecks.append("内存使用率过高，可能存在内存泄漏或需要增加内存")
        
        # 响应时间瓶颈
        if avg_response_time > 5:
            bottlenecks.append("响应时间过长，可能是数据库查询或API调用缓慢")
        
        # 成功率瓶颈
        if avg_success_rate < 0.95:
            bottlenecks.append("成功率过低，可能存在系统错误或API限流")
        
        return bottlenecks
    
    def generate_optimization_suggestions(self, analysis: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        for metric, data in analysis.items():
            if data.get("degraded", False):
                change = data["change_percent"]
                
                if metric == "avg_response_time" and change > 20:
                    suggestions.append("考虑优化数据库查询或增加缓存")
                elif metric == "cpu_percent" and change > 30:
                    suggestions.append("考虑优化CPU密集型操作或增加服务器资源")
                elif metric == "memory_percent" and change > 25:
                    suggestions.append("检查内存泄漏或考虑增加内存容量")
                elif metric == "qps" and change < -15:
                    suggestions.append("检查API限流设置或优化请求处理逻辑")
                elif metric == "success_rate" and change < -5:
                    suggestions.append("检查错误日志，修复导致失败的问题")
        
        return suggestions
