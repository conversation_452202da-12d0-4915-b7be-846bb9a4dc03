#!/usr/bin/env python3
"""
压力测试报告生成系统

生成详细的测试报告，包括性能曲线图表生成、HTML报告模板、
优化建议算法和结果数据导出。
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from jinja2 import Template
import base64
from io import BytesIO

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")


class ReportGenerator:
    """压力测试报告生成器"""
    
    def __init__(self, output_dir: str = "test_reports"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def generate_performance_charts(self, test_results: Dict[str, Any]) -> Dict[str, str]:
        """生成性能图表"""
        charts = {}
        
        # 1. 并发用户数 vs 响应时间图表
        charts['response_time_chart'] = self._create_response_time_chart(test_results)
        
        # 2. 并发用户数 vs QPS图表
        charts['qps_chart'] = self._create_qps_chart(test_results)
        
        # 3. 成功率图表
        charts['success_rate_chart'] = self._create_success_rate_chart(test_results)
        
        # 4. 系统资源使用图表
        charts['resource_usage_chart'] = self._create_resource_usage_chart(test_results)
        
        # 5. API限流使用情况图表
        charts['rate_limit_chart'] = self._create_rate_limit_chart(test_results)
        
        # 6. 响应时间分布图表
        charts['response_distribution_chart'] = self._create_response_distribution_chart(test_results)
        
        return charts
    
    def _create_response_time_chart(self, test_results: Dict[str, Any]) -> str:
        """创建响应时间图表"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        concurrent_users = []
        avg_times = []
        p95_times = []
        p99_times = []
        
        for step_result in test_results.get('step_results', []):
            concurrent_users.append(step_result['concurrent_users'])
            stats = step_result.get('performance_stats', {})
            avg_times.append(stats.get('avg_response_time', 0))
            p95_times.append(stats.get('p95_response_time', 0))
            p99_times.append(stats.get('p99_response_time', 0))
        
        ax.plot(concurrent_users, avg_times, 'o-', label='平均响应时间', linewidth=2)
        ax.plot(concurrent_users, p95_times, 's-', label='P95响应时间', linewidth=2)
        ax.plot(concurrent_users, p99_times, '^-', label='P99响应时间', linewidth=2)
        
        ax.set_xlabel('并发用户数')
        ax.set_ylabel('响应时间 (秒)')
        ax.set_title('并发用户数 vs 响应时间')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        return self._fig_to_base64(fig)
    
    def _create_qps_chart(self, test_results: Dict[str, Any]) -> str:
        """创建QPS图表"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        concurrent_users = []
        qps_values = []
        
        for step_result in test_results.get('step_results', []):
            concurrent_users.append(step_result['concurrent_users'])
            stats = step_result.get('performance_stats', {})
            qps_values.append(stats.get('qps', 0))
        
        ax.plot(concurrent_users, qps_values, 'o-', color='green', linewidth=2, markersize=8)
        ax.set_xlabel('并发用户数')
        ax.set_ylabel('QPS (每秒查询数)')
        ax.set_title('并发用户数 vs QPS')
        ax.grid(True, alpha=0.3)
        
        # 添加最优点标注
        if qps_values:
            max_qps_idx = qps_values.index(max(qps_values))
            ax.annotate(f'最优QPS: {max(qps_values):.1f}\n并发数: {concurrent_users[max_qps_idx]}',
                       xy=(concurrent_users[max_qps_idx], max(qps_values)),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        return self._fig_to_base64(fig)
    
    def _create_success_rate_chart(self, test_results: Dict[str, Any]) -> str:
        """创建成功率图表"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        concurrent_users = []
        success_rates = []
        
        for step_result in test_results.get('step_results', []):
            concurrent_users.append(step_result['concurrent_users'])
            stats = step_result.get('performance_stats', {})
            success_rates.append(stats.get('success_rate', 0) * 100)
        
        ax.plot(concurrent_users, success_rates, 'o-', color='blue', linewidth=2, markersize=8)
        ax.set_xlabel('并发用户数')
        ax.set_ylabel('成功率 (%)')
        ax.set_title('并发用户数 vs 成功率')
        ax.set_ylim(0, 105)
        ax.grid(True, alpha=0.3)
        
        # 添加95%成功率基线
        ax.axhline(y=95, color='red', linestyle='--', alpha=0.7, label='95%基线')
        ax.legend()
        
        return self._fig_to_base64(fig)
    
    def _create_resource_usage_chart(self, test_results: Dict[str, Any]) -> str:
        """创建系统资源使用图表"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        concurrent_users = []
        cpu_usage = []
        memory_usage = []
        
        for step_result in test_results.get('step_results', []):
            concurrent_users.append(step_result['concurrent_users'])
            system_stats = step_result.get('system_stats', {})
            cpu_usage.append(system_stats.get('avg_cpu_percent', 0))
            memory_usage.append(system_stats.get('avg_memory_percent', 0))
        
        # CPU使用率
        ax1.plot(concurrent_users, cpu_usage, 'o-', color='red', linewidth=2)
        ax1.set_ylabel('CPU使用率 (%)')
        ax1.set_title('系统资源使用情况')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=80, color='orange', linestyle='--', alpha=0.7, label='80%警告线')
        ax1.legend()
        
        # 内存使用率
        ax2.plot(concurrent_users, memory_usage, 'o-', color='purple', linewidth=2)
        ax2.set_xlabel('并发用户数')
        ax2.set_ylabel('内存使用率 (%)')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=85, color='orange', linestyle='--', alpha=0.7, label='85%警告线')
        ax2.legend()
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def _create_rate_limit_chart(self, test_results: Dict[str, Any]) -> str:
        """创建API限流使用情况图表"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        concurrent_users = []
        qpm_usage = []
        tpm_usage = []
        
        for step_result in test_results.get('step_results', []):
            concurrent_users.append(step_result['concurrent_users'])
            rate_limit = step_result.get('rate_limit_status', {})
            qpm_usage.append(rate_limit.get('qpm_usage_percent', 0))
            tpm_usage.append(rate_limit.get('tpm_usage_percent', 0))
        
        # QPM使用率
        ax1.plot(concurrent_users, qpm_usage, 'o-', color='blue', linewidth=2)
        ax1.set_ylabel('QPM使用率 (%)')
        ax1.set_title('API限流使用情况')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80%安全线')
        ax1.legend()
        
        # TPM使用率
        ax2.plot(concurrent_users, tpm_usage, 'o-', color='green', linewidth=2)
        ax2.set_xlabel('并发用户数')
        ax2.set_ylabel('TPM使用率 (%)')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80%安全线')
        ax2.legend()
        
        plt.tight_layout()
        return self._fig_to_base64(fig)
    
    def _create_response_distribution_chart(self, test_results: Dict[str, Any]) -> str:
        """创建响应时间分布图表"""
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 收集所有响应时间数据
        all_response_times = []
        for step_result in test_results.get('step_results', []):
            response_times = step_result.get('raw_response_times', [])
            all_response_times.extend(response_times)
        
        if all_response_times:
            ax.hist(all_response_times, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax.set_xlabel('响应时间 (秒)')
            ax.set_ylabel('频次')
            ax.set_title('响应时间分布直方图')
            ax.grid(True, alpha=0.3)
            
            # 添加统计信息
            import statistics
            mean_time = statistics.mean(all_response_times)
            median_time = statistics.median(all_response_times)
            ax.axvline(mean_time, color='red', linestyle='--', label=f'平均值: {mean_time:.2f}s')
            ax.axvline(median_time, color='green', linestyle='--', label=f'中位数: {median_time:.2f}s')
            ax.legend()
        
        return self._fig_to_base64(fig)
    
    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图表转换为base64字符串"""
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close(fig)
        return f"data:image/png;base64,{image_base64}"
    
    def generate_html_report(self, test_results: Dict[str, Any], 
                           charts: Dict[str, str]) -> str:
        """生成HTML报告"""
        template_str = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBI压力测试报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .summary { background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .metric { display: inline-block; margin: 10px 20px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2980b9; }
        .metric-label { font-size: 14px; color: #7f8c8d; }
        .chart { text-align: center; margin: 30px 0; }
        .chart img { max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 5px; }
        .recommendations { background: #d5f4e6; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .warning { background: #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #fab1a0; padding: 15px; border-radius: 5px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #3498db; color: white; }
        .footer { text-align: center; margin-top: 40px; color: #7f8c8d; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ChatBI系统压力测试报告</h1>
        
        <div class="summary">
            <h2>测试概要</h2>
            <div class="metric">
                <div class="metric-value">{{ test_summary.max_concurrent_users }}</div>
                <div class="metric-label">最大并发用户数</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(test_summary.peak_qps) }}</div>
                <div class="metric-label">峰值QPS</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(test_summary.avg_response_time) }}s</div>
                <div class="metric-label">平均响应时间</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ "%.1f"|format(test_summary.success_rate * 100) }}%</div>
                <div class="metric-label">总体成功率</div>
            </div>
        </div>

        <h2>性能图表</h2>
        
        <div class="chart">
            <h3>响应时间趋势</h3>
            <img src="{{ charts.response_time_chart }}" alt="响应时间图表">
        </div>
        
        <div class="chart">
            <h3>QPS性能</h3>
            <img src="{{ charts.qps_chart }}" alt="QPS图表">
        </div>
        
        <div class="chart">
            <h3>成功率</h3>
            <img src="{{ charts.success_rate_chart }}" alt="成功率图表">
        </div>
        
        <div class="chart">
            <h3>系统资源使用</h3>
            <img src="{{ charts.resource_usage_chart }}" alt="资源使用图表">
        </div>
        
        <div class="chart">
            <h3>API限流使用情况</h3>
            <img src="{{ charts.rate_limit_chart }}" alt="限流图表">
        </div>
        
        <div class="chart">
            <h3>响应时间分布</h3>
            <img src="{{ charts.response_distribution_chart }}" alt="响应时间分布图表">
        </div>

        <h2>详细测试结果</h2>
        <table>
            <tr>
                <th>并发用户数</th>
                <th>QPS</th>
                <th>平均响应时间(s)</th>
                <th>P95响应时间(s)</th>
                <th>成功率(%)</th>
                <th>CPU使用率(%)</th>
                <th>内存使用率(%)</th>
            </tr>
            {% for result in step_results %}
            <tr>
                <td>{{ result.concurrent_users }}</td>
                <td>{{ "%.1f"|format(result.performance_stats.qps) }}</td>
                <td>{{ "%.2f"|format(result.performance_stats.avg_response_time) }}</td>
                <td>{{ "%.2f"|format(result.performance_stats.p95_response_time) }}</td>
                <td>{{ "%.1f"|format(result.performance_stats.success_rate * 100) }}</td>
                <td>{{ "%.1f"|format(result.system_stats.avg_cpu_percent) }}</td>
                <td>{{ "%.1f"|format(result.system_stats.avg_memory_percent) }}</td>
            </tr>
            {% endfor %}
        </table>

        <div class="recommendations">
            <h2>性能建议</h2>
            {% for recommendation in recommendations %}
            <div class="warning">{{ recommendation }}</div>
            {% endfor %}
        </div>

        <div class="footer">
            <p>报告生成时间: {{ report_time }}</p>
            <p>ChatBI压力测试系统 v1.0</p>
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(template_str)
        
        # 准备模板数据
        template_data = {
            'test_summary': self._generate_test_summary(test_results),
            'charts': charts,
            'step_results': test_results.get('step_results', []),
            'recommendations': self._generate_recommendations(test_results),
            'report_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return template.render(**template_data)
    
    def _generate_test_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试摘要"""
        step_results = test_results.get('step_results', [])
        
        if not step_results:
            return {}
        
        max_concurrent = max(r['concurrent_users'] for r in step_results)
        peak_qps = max(r.get('performance_stats', {}).get('qps', 0) for r in step_results)
        
        # 计算总体平均响应时间和成功率
        total_requests = sum(r.get('performance_stats', {}).get('total_requests', 0) for r in step_results)
        total_successful = sum(r.get('performance_stats', {}).get('successful_requests', 0) for r in step_results)
        
        avg_response_times = [r.get('performance_stats', {}).get('avg_response_time', 0) for r in step_results]
        overall_avg_response_time = sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0
        
        overall_success_rate = total_successful / total_requests if total_requests > 0 else 0
        
        return {
            'max_concurrent_users': max_concurrent,
            'peak_qps': peak_qps,
            'avg_response_time': overall_avg_response_time,
            'success_rate': overall_success_rate
        }
    
    def _generate_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        step_results = test_results.get('step_results', [])
        
        if not step_results:
            return recommendations
        
        # 分析最后几个测试步骤的性能
        recent_results = step_results[-3:] if len(step_results) >= 3 else step_results
        
        # 检查成功率
        avg_success_rate = sum(r.get('performance_stats', {}).get('success_rate', 0) for r in recent_results) / len(recent_results)
        if avg_success_rate < 0.95:
            recommendations.append("系统成功率低于95%，建议检查错误日志并优化错误处理机制")
        
        # 检查响应时间
        avg_response_time = sum(r.get('performance_stats', {}).get('avg_response_time', 0) for r in recent_results) / len(recent_results)
        if avg_response_time > 5:
            recommendations.append("平均响应时间超过5秒，建议优化数据库查询或增加缓存")
        
        # 检查CPU使用率
        avg_cpu = sum(r.get('system_stats', {}).get('avg_cpu_percent', 0) for r in recent_results) / len(recent_results)
        if avg_cpu > 80:
            recommendations.append("CPU使用率过高，建议优化计算密集型操作或增加服务器资源")
        
        # 检查内存使用率
        avg_memory = sum(r.get('system_stats', {}).get('avg_memory_percent', 0) for r in recent_results) / len(recent_results)
        if avg_memory > 85:
            recommendations.append("内存使用率过高，建议检查内存泄漏或增加内存容量")
        
        # 检查API限流使用情况
        for result in recent_results:
            rate_limit = result.get('rate_limit_status', {})
            if rate_limit.get('qpm_usage_percent', 0) > 90:
                recommendations.append("QPM使用率接近限制，建议优化API调用频率或申请更高的限额")
            if rate_limit.get('tpm_usage_percent', 0) > 90:
                recommendations.append("TPM使用率接近限制，建议优化查询复杂度或申请更高的Token限额")
        
        if not recommendations:
            recommendations.append("系统性能表现良好，各项指标均在正常范围内")
        
        return recommendations
    
    def save_report(self, html_content: str, filename: str = None) -> str:
        """保存HTML报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"chatbi_stress_test_report_{timestamp}.html"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filepath
    
    def export_raw_data(self, test_results: Dict[str, Any], filename: str = None) -> str:
        """导出原始测试数据"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"chatbi_stress_test_data_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        return filepath
