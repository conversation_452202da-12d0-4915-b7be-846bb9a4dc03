import logging
import uvicorn
from app import create_app

# ----------------------
# 日志配置：只显示关键信息
# ----------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s"
)

# 设置各个模块的日志级别
logging.getLogger("uvicorn.access").setLevel(logging.WARNING)  # Uvicorn访问日志
logging.getLogger("openai").setLevel(logging.WARNING)    # OpenAI客户端日志
logging.getLogger("httpx").setLevel(logging.WARNING)     # HTTP请求日志
logging.getLogger("httpcore").setLevel(logging.WARNING)  # HTTP核心日志
logging.getLogger("langchain_core").setLevel(logging.WARNING)  # LangChain核心日志
logging.getLogger("asyncio").setLevel(logging.WARNING)   # 异步IO日志

# ----------------------
# FastAPI 应用启动
# ----------------------
app = create_app()

if __name__ == '__main__':
    # 使用Uvicorn启动FastAPI应用，支持热重载
    uvicorn.run(
        "run:app",
        host="0.0.0.0",
        port=5000,
        reload=True,  # 开发模式下启用热重载
        log_level="info"
    )