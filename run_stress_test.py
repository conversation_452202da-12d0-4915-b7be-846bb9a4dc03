#!/usr/bin/env python3
"""
ChatBI压力测试快速启动脚本

提供简化的测试启动界面，自动检查环境和配置。
"""

import os
import sys
import asyncio
import subprocess
import time
from typing import Dict, Any

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'aiohttp', 'matplotlib', 'seaborn', 'psutil', 'jinja2'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_chatbi_service(base_url: str = "http://localhost:5000"):
    """检查ChatBI服务状态"""
    try:
        import requests
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ ChatBI服务正在运行")
            return True
        else:
            print(f"❌ ChatBI服务响应异常: {response.status_code}")
            return False
    except ImportError:
        print("⚠️  无法检查服务状态 (缺少requests包)")
        return True  # 假设服务正常
    except Exception as e:
        print(f"❌ 无法连接到ChatBI服务: {e}")
        print("请确保ChatBI服务正在运行:")
        print("  python run.py")
        return False

def get_test_config():
    """获取测试配置"""
    print("\n=== ChatBI压力测试配置 ===")
    
    configs = {
        "1": {
            "name": "快速测试 (推荐)",
            "max_users": 100,
            "duration": 1,
            "description": "适合快速验证系统性能"
        },
        "2": {
            "name": "标准测试",
            "max_users": 500,
            "duration": 2,
            "description": "全面的性能测试"
        },
        "3": {
            "name": "压力测试",
            "max_users": 1000,
            "duration": 3,
            "description": "高强度压力测试"
        },
        "4": {
            "name": "自定义配置",
            "max_users": None,
            "duration": None,
            "description": "手动配置测试参数"
        }
    }
    
    print("请选择测试配置:")
    for key, config in configs.items():
        print(f"  {key}. {config['name']} - {config['description']}")
        if config['max_users']:
            print(f"     (最大{config['max_users']}并发用户, {config['duration']}分钟/步)")
    
    while True:
        choice = input("\n请输入选择 (1-4): ").strip()
        if choice in configs:
            selected_config = configs[choice]
            break
        print("无效选择，请重新输入")
    
    if choice == "4":
        # 自定义配置
        while True:
            try:
                max_users = int(input("最大并发用户数 (默认500): ") or "500")
                duration = int(input("每步测试时长/分钟 (默认2): ") or "2")
                break
            except ValueError:
                print("请输入有效的数字")
        
        selected_config["max_users"] = max_users
        selected_config["duration"] = duration
    
    return selected_config

def estimate_test_time(config: Dict[str, Any]):
    """估算测试时间"""
    # 默认测试步骤: [10, 50, 100, 200, 500, 1000]
    steps = [10, 50, 100, 200]
    actual_steps = [s for s in steps if s <= config["max_users"]]
    
    total_time = len(actual_steps) * config["duration"]
    
    print(f"\n📊 测试预估:")
    print(f"   测试步骤: {actual_steps}")
    print(f"   预估总时长: {total_time} 分钟")
    print(f"   最大并发: {config['max_users']} 用户")

def run_test(config: Dict[str, Any]):
    """运行压力测试"""
    print(f"\n🚀 开始运行 {config['name']}")
    
    # 构建命令
    cmd = [
        sys.executable, "chatbi_stress_test.py",
        "--max-users", str(config["max_users"]),
        "--duration", str(config["duration"])
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, check=True)
        
        print("=" * 60)
        print("✅ 压力测试完成!")
        
        # 查找生成的报告
        report_dir = "test_reports"
        if os.path.exists(report_dir):
            reports = [f for f in os.listdir(report_dir) if f.endswith('.html')]
            if reports:
                latest_report = max(reports, key=lambda x: os.path.getctime(os.path.join(report_dir, x)))
                report_path = os.path.join(report_dir, latest_report)
                print(f"📊 测试报告: {report_path}")
                
                # 询问是否打开报告
                if input("\n是否打开测试报告? (y/n): ").lower() == 'y':
                    try:
                        import webbrowser
                        webbrowser.open(f"file://{os.path.abspath(report_path)}")
                    except Exception as e:
                        print(f"无法自动打开报告: {e}")
                        print(f"请手动打开: {os.path.abspath(report_path)}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
        return False
    
    return True

def main():
    """主函数"""
    print("🔥 ChatBI系统压力测试工具")
    print("=" * 40)
    
    # 1. 检查依赖
    print("\n1. 检查依赖包...")
    if not check_dependencies():
        return
    
    # 2. 检查服务
    print("\n2. 检查ChatBI服务...")
    if not check_chatbi_service():
        return
    
    # 3. 获取配置
    config = get_test_config()
    
    # 4. 估算时间
    estimate_test_time(config)
    
    # 5. 确认开始
    print(f"\n⚠️  注意: 压力测试会对系统产生较高负载")
    if input("确认开始测试? (y/n): ").lower() != 'y':
        print("测试已取消")
        return
    
    # 6. 运行测试
    success = run_test(config)
    
    if success:
        print("\n🎉 测试完成! 请查看生成的报告了解详细结果。")
    else:
        print("\n💥 测试未能正常完成，请检查日志文件 stress_test.log")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 再见!")
    except Exception as e:
        print(f"\n❌ 发生意外错误: {e}")
        print("请检查环境配置或联系技术支持")
