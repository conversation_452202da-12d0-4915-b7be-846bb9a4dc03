#!/usr/bin/env python3
"""
流式响应测试处理器

专门处理ChatBI系统的Server-Sent Events (SSE) 流式响应测试。
包括首字节时间测量、流式传输性能分析和事件完整性检查。
"""

import asyncio
import aiohttp
import time
import json
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SSEEventType(Enum):
    """SSE事件类型枚举"""
    SUMMARY_CHUNK = "summary_chunk"
    SQL_GENERATED = "sql_generated"
    DATA_FINAL = "data_final"
    BITWISE_DECODED = "bitwise_decoded"
    FINAL_SUMMARY_START = "final_summary_start"
    FINAL_SUMMARY_CHUNK = "final_summary_chunk"
    ERROR = "error"


@dataclass
class SSEEvent:
    """SSE事件数据结构"""
    event_type: str
    data: Dict[str, Any]
    timestamp: float
    raw_data: str


@dataclass
class StreamingMetrics:
    """流式响应性能指标"""
    request_start_time: float
    first_byte_time: Optional[float] = None
    last_byte_time: Optional[float] = None
    total_events: int = 0
    events_by_type: Dict[str, int] = None
    total_bytes: int = 0
    stream_duration: float = 0
    success: bool = False
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.events_by_type is None:
            self.events_by_type = {}


class StreamingTestHandler:
    """流式响应测试处理器"""
    
    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        self.expected_event_sequence = [
            SSEEventType.SUMMARY_CHUNK,
            SSEEventType.SQL_GENERATED,
            SSEEventType.DATA_FINAL,
            SSEEventType.FINAL_SUMMARY_START,
            SSEEventType.FINAL_SUMMARY_CHUNK
        ]
    
    async def test_streaming_query(self, base_url: str, question: str, 
                                 timeout: int = 60) -> StreamingMetrics:
        """
        测试流式查询响应
        
        Args:
            base_url: 基础URL
            question: 查询问题
            timeout: 超时时间
            
        Returns:
            StreamingMetrics: 流式响应性能指标
        """
        url = f"{base_url}/api/query"
        payload = {"question": question}
        
        metrics = StreamingMetrics(request_start_time=time.time())
        
        try:
            async with self.session.post(url, json=payload) as response:
                if response.status != 200:
                    metrics.error_message = f"HTTP {response.status}: {await response.text()}"
                    return metrics
                
                # 验证响应头
                content_type = response.headers.get('content-type', '')
                if 'text/event-stream' not in content_type:
                    metrics.error_message = f"Invalid content-type: {content_type}"
                    return metrics
                
                # 处理流式响应
                await self._process_stream(response, metrics, timeout)
                
        except asyncio.TimeoutError:
            metrics.error_message = "Request timeout"
        except Exception as e:
            metrics.error_message = f"Request error: {str(e)}"
        
        return metrics
    
    async def _process_stream(self, response: aiohttp.ClientResponse, 
                            metrics: StreamingMetrics, timeout: int):
        """处理流式响应数据"""
        events = []
        current_event = None
        current_data = None
        
        try:
            async with asyncio.timeout(timeout):
                async for line in response.content:
                    line_str = line.decode('utf-8').strip()
                    
                    # 记录首字节时间
                    if metrics.first_byte_time is None:
                        metrics.first_byte_time = time.time()
                    
                    metrics.total_bytes += len(line)
                    
                    if not line_str:
                        # 空行表示事件结束
                        if current_event and current_data:
                            event = self._parse_sse_event(current_event, current_data)
                            if event:
                                events.append(event)
                                metrics.total_events += 1
                                metrics.events_by_type[event.event_type] = \
                                    metrics.events_by_type.get(event.event_type, 0) + 1
                        
                        current_event = None
                        current_data = None
                        continue
                    
                    # 解析SSE格式
                    if line_str.startswith('event:'):
                        current_event = line_str[6:].strip()
                    elif line_str.startswith('data:'):
                        current_data = line_str[5:].strip()
            
            metrics.last_byte_time = time.time()
            metrics.stream_duration = metrics.last_byte_time - metrics.request_start_time
            
            # 验证事件完整性
            metrics.success = self._validate_event_sequence(events)
            
        except asyncio.TimeoutError:
            metrics.error_message = "Stream processing timeout"
        except Exception as e:
            metrics.error_message = f"Stream processing error: {str(e)}"
    
    def _parse_sse_event(self, event_type: str, data: str) -> Optional[SSEEvent]:
        """解析SSE事件"""
        try:
            parsed_data = json.loads(data) if data else {}
            return SSEEvent(
                event_type=event_type,
                data=parsed_data,
                timestamp=time.time(),
                raw_data=data
            )
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse SSE data: {data}")
            return None
    
    def _validate_event_sequence(self, events: List[SSEEvent]) -> bool:
        """验证事件序列的完整性"""
        if not events:
            return False
        
        # 检查是否包含关键事件
        event_types = [event.event_type for event in events]
        
        # 必须包含的事件
        required_events = ["summary_chunk", "sql_generated", "data_final"]
        for required in required_events:
            if required not in event_types:
                logger.warning(f"Missing required event: {required}")
                return False
        
        # 检查事件顺序（简化版本）
        if "sql_generated" in event_types and "data_final" in event_types:
            sql_index = event_types.index("sql_generated")
            data_index = event_types.index("data_final")
            if sql_index >= data_index:
                logger.warning("Event sequence error: sql_generated should come before data_final")
                return False
        
        return True
    
    async def test_concurrent_streams(self, base_url: str, questions: List[str], 
                                    concurrent_count: int) -> List[StreamingMetrics]:
        """
        测试并发流式查询
        
        Args:
            base_url: 基础URL
            questions: 查询问题列表
            concurrent_count: 并发数量
            
        Returns:
            List[StreamingMetrics]: 所有流式响应的性能指标
        """
        # 创建并发任务
        tasks = []
        for i in range(concurrent_count):
            question = questions[i % len(questions)]
            task = asyncio.create_task(
                self.test_streaming_query(base_url, question)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        metrics_list = []
        for result in results:
            if isinstance(result, StreamingMetrics):
                metrics_list.append(result)
            elif isinstance(result, Exception):
                # 创建错误指标
                error_metrics = StreamingMetrics(
                    request_start_time=time.time(),
                    error_message=str(result)
                )
                metrics_list.append(error_metrics)
        
        return metrics_list
    
    def analyze_streaming_performance(self, metrics_list: List[StreamingMetrics]) -> Dict[str, Any]:
        """
        分析流式响应性能
        
        Args:
            metrics_list: 流式响应指标列表
            
        Returns:
            Dict[str, Any]: 性能分析结果
        """
        if not metrics_list:
            return {}
        
        successful_metrics = [m for m in metrics_list if m.success]
        failed_metrics = [m for m in metrics_list if not m.success]
        
        analysis = {
            "total_requests": len(metrics_list),
            "successful_requests": len(successful_metrics),
            "failed_requests": len(failed_metrics),
            "success_rate": len(successful_metrics) / len(metrics_list) if metrics_list else 0
        }
        
        if successful_metrics:
            # 首字节时间分析
            first_byte_times = [
                m.first_byte_time - m.request_start_time 
                for m in successful_metrics 
                if m.first_byte_time
            ]
            
            if first_byte_times:
                analysis["first_byte_time"] = {
                    "avg": sum(first_byte_times) / len(first_byte_times),
                    "min": min(first_byte_times),
                    "max": max(first_byte_times),
                    "p95": self._percentile(first_byte_times, 95)
                }
            
            # 流式传输时间分析
            stream_durations = [m.stream_duration for m in successful_metrics if m.stream_duration > 0]
            if stream_durations:
                analysis["stream_duration"] = {
                    "avg": sum(stream_durations) / len(stream_durations),
                    "min": min(stream_durations),
                    "max": max(stream_durations),
                    "p95": self._percentile(stream_durations, 95)
                }
            
            # 事件统计
            total_events = sum(m.total_events for m in successful_metrics)
            analysis["events"] = {
                "total_events": total_events,
                "avg_events_per_request": total_events / len(successful_metrics),
                "events_by_type": self._aggregate_events_by_type(successful_metrics)
            }
            
            # 数据传输统计
            total_bytes = sum(m.total_bytes for m in successful_metrics)
            analysis["data_transfer"] = {
                "total_bytes": total_bytes,
                "avg_bytes_per_request": total_bytes / len(successful_metrics),
                "avg_transfer_rate_kbps": (total_bytes / 1024) / (sum(stream_durations) / len(stream_durations)) if stream_durations else 0
            }
        
        # 错误分析
        if failed_metrics:
            error_types = {}
            for m in failed_metrics:
                error_type = m.error_message or "Unknown error"
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            analysis["errors"] = {
                "error_types": error_types,
                "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
            }
        
        return analysis
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _aggregate_events_by_type(self, metrics_list: List[StreamingMetrics]) -> Dict[str, int]:
        """聚合事件类型统计"""
        aggregated = {}
        for metrics in metrics_list:
            for event_type, count in metrics.events_by_type.items():
                aggregated[event_type] = aggregated.get(event_type, 0) + count
        return aggregated
