#!/usr/bin/env python3
"""
ChatBI压力测试核心框架

提供异步HTTP客户端、并发控制、性能指标收集等核心功能。
支持智能限流控制和真实业务场景模拟。
"""

import asyncio
import aiohttp
import time
import json
import logging
import psutil
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import statistics
from collections import defaultdict, deque

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class TestConfig:
    """测试配置类"""
    base_url: str = "http://localhost:5000"
    max_concurrent_users: int = 100
    test_duration_minutes: int = 2
    ramp_up_steps: List[int] = field(default_factory=lambda: [10, 50, 100, 200, 500, 1000])
    
    # API限流配置
    qpm_limit: int = 1200  # 每分钟查询次数限制
    tpm_limit: int = 5000000  # 每分钟Token限制
    safety_margin: float = 0.8  # 安全边界，使用限制的80%
    
    # 请求超时配置
    request_timeout: int = 60
    connection_timeout: int = 10


@dataclass
class PerformanceMetrics:
    """性能指标类"""
    timestamp: float
    response_time: float
    status_code: int
    success: bool
    error_message: Optional[str] = None
    tokens_used: int = 0
    endpoint: str = ""
    concurrent_users: int = 0


@dataclass
class SystemMetrics:
    """系统资源指标类"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    active_connections: int = 0


class PerformanceCollector:
    """性能指标收集器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        self.start_time = time.time()
        self.lock = asyncio.Lock()
    
    async def add_metric(self, metric: PerformanceMetrics):
        """添加性能指标"""
        async with self.lock:
            self.metrics.append(metric)
    
    async def add_system_metric(self, metric: SystemMetrics):
        """添加系统指标"""
        async with self.lock:
            self.system_metrics.append(metric)
    
    def get_statistics(self, time_window_seconds: Optional[int] = None) -> Dict[str, Any]:
        """获取性能统计信息"""
        if not self.metrics:
            return {}
        
        # 过滤时间窗口内的指标
        if time_window_seconds:
            cutoff_time = time.time() - time_window_seconds
            filtered_metrics = [m for m in self.metrics if m.timestamp >= cutoff_time]
        else:
            filtered_metrics = self.metrics
        
        if not filtered_metrics:
            return {}
        
        # 计算统计信息
        response_times = [m.response_time for m in filtered_metrics if m.success]
        success_count = sum(1 for m in filtered_metrics if m.success)
        total_count = len(filtered_metrics)
        
        stats = {
            "total_requests": total_count,
            "successful_requests": success_count,
            "failed_requests": total_count - success_count,
            "success_rate": success_count / total_count if total_count > 0 else 0,
            "test_duration": time.time() - self.start_time
        }
        
        if response_times:
            stats.update({
                "avg_response_time": statistics.mean(response_times),
                "min_response_time": min(response_times),
                "max_response_time": max(response_times),
                "p50_response_time": statistics.median(response_times),
                "p95_response_time": self._percentile(response_times, 95),
                "p99_response_time": self._percentile(response_times, 99),
                "qps": len(response_times) / (time.time() - self.start_time)
            })
        
        return stats
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


class RateLimiter:
    """API限流控制器"""
    
    def __init__(self, qpm_limit: int, tpm_limit: int, safety_margin: float = 0.8):
        self.qpm_limit = int(qpm_limit * safety_margin)
        self.tpm_limit = int(tpm_limit * safety_margin)
        
        # 使用滑动窗口记录API调用
        self.call_times = deque()
        self.token_usage = deque()
        self.lock = asyncio.Lock()
    
    async def can_make_request(self, estimated_tokens: int = 100) -> bool:
        """检查是否可以发起请求"""
        async with self.lock:
            current_time = time.time()
            
            # 清理1分钟前的记录
            cutoff_time = current_time - 60
            while self.call_times and self.call_times[0] < cutoff_time:
                self.call_times.popleft()
            while self.token_usage and self.token_usage[0][0] < cutoff_time:
                self.token_usage.popleft()
            
            # 检查QPM限制
            if len(self.call_times) >= self.qpm_limit:
                return False
            
            # 检查TPM限制
            current_tokens = sum(tokens for _, tokens in self.token_usage)
            if current_tokens + estimated_tokens > self.tpm_limit:
                return False
            
            return True
    
    async def record_request(self, tokens_used: int = 100):
        """记录API请求"""
        async with self.lock:
            current_time = time.time()
            self.call_times.append(current_time)
            self.token_usage.append((current_time, tokens_used))
    
    async def get_current_usage(self) -> Dict[str, Any]:
        """获取当前使用情况"""
        async with self.lock:
            current_time = time.time()
            cutoff_time = current_time - 60
            
            # 计算当前QPM
            recent_calls = [t for t in self.call_times if t >= cutoff_time]
            current_qpm = len(recent_calls)
            
            # 计算当前TPM
            recent_tokens = [tokens for t, tokens in self.token_usage if t >= cutoff_time]
            current_tpm = sum(recent_tokens)
            
            return {
                "current_qpm": current_qpm,
                "qpm_limit": self.qpm_limit,
                "qpm_usage_percent": (current_qpm / self.qpm_limit) * 100,
                "current_tpm": current_tpm,
                "tpm_limit": self.tpm_limit,
                "tpm_usage_percent": (current_tpm / self.tpm_limit) * 100
            }


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.monitoring = False
        self.monitor_task = None
    
    async def start_monitoring(self, interval: float = 1.0):
        """开始系统监控"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
    
    async def stop_monitoring(self):
        """停止系统监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                
                metric = SystemMetrics(
                    timestamp=time.time(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    memory_used_mb=memory.used / 1024 / 1024
                )
                
                await self.collector.add_system_metric(metric)
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"系统监控错误: {e}")
                await asyncio.sleep(interval)


class StressTestFramework:
    """压力测试框架主类"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.collector = PerformanceCollector()
        self.rate_limiter = RateLimiter(
            config.qpm_limit, 
            config.tpm_limit, 
            config.safety_margin
        )
        self.system_monitor = SystemMonitor(self.collector)
        self.session: Optional[aiohttp.ClientSession] = None
        self.active_tasks: List[asyncio.Task] = []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        timeout = aiohttp.ClientTimeout(
            total=self.config.request_timeout,
            connect=self.config.connection_timeout
        )
        self.session = aiohttp.ClientSession(timeout=timeout)
        await self.system_monitor.start_monitoring()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 取消所有活跃任务
        for task in self.active_tasks:
            task.cancel()
        
        if self.active_tasks:
            await asyncio.gather(*self.active_tasks, return_exceptions=True)
        
        await self.system_monitor.stop_monitoring()
        
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, data: Dict[str, Any], 
                          concurrent_users: int, estimated_tokens: int = 100) -> PerformanceMetrics:
        """发起单个请求"""
        start_time = time.time()
        
        # 检查限流
        if not await self.rate_limiter.can_make_request(estimated_tokens):
            return PerformanceMetrics(
                timestamp=start_time,
                response_time=0,
                status_code=429,
                success=False,
                error_message="Rate limit exceeded",
                tokens_used=0,
                endpoint=endpoint,
                concurrent_users=concurrent_users
            )
        
        try:
            url = f"{self.config.base_url}{endpoint}"
            
            async with self.session.request(method, url, json=data) as response:
                response_time = time.time() - start_time
                
                # 记录API使用
                await self.rate_limiter.record_request(estimated_tokens)
                
                # 处理不同类型的响应
                if endpoint == "/api/query":
                    # 流式响应处理
                    await self._handle_streaming_response(response)
                else:
                    # 普通响应处理
                    await response.text()
                
                return PerformanceMetrics(
                    timestamp=start_time,
                    response_time=response_time,
                    status_code=response.status,
                    success=response.status == 200,
                    tokens_used=estimated_tokens,
                    endpoint=endpoint,
                    concurrent_users=concurrent_users
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return PerformanceMetrics(
                timestamp=start_time,
                response_time=response_time,
                status_code=0,
                success=False,
                error_message=str(e),
                tokens_used=0,
                endpoint=endpoint,
                concurrent_users=concurrent_users
            )
    
    async def _handle_streaming_response(self, response):
        """处理流式响应"""
        async for line in response.content:
            if line:
                # 简单处理SSE数据
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data:'):
                    # 解析SSE数据
                    pass
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self.collector.get_statistics()
    
    async def get_rate_limit_status(self) -> Dict[str, Any]:
        """获取限流状态"""
        return await self.rate_limiter.get_current_usage()
