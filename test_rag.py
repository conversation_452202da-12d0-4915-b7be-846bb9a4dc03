#!/usr/bin/env python3
"""
RAG系统最终测试
"""

import asyncio
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_rag_system():
    """测试RAG系统"""
    print("=== 测试Schema RAG系统 ===")
    
    try:
        # 获取RAG协调器
        from app.services.schema_embedding_service import get_schema_rag_orchestrator
        
        orchestrator = get_schema_rag_orchestrator()
        await orchestrator.initialize()
        
        # 测试几个查询
        test_queries = [
            "查找开发成功的专家",
            "统计专家数量",
            "专家状态分布",
            "按领域分组",
            "专家工作经历",
            "专家申请记录"
        ]
        
        print("\n=== RAG检索测试结果 ===")
        for query in test_queries:
            print(f"\n查询: {query}")
            try:
                context = await orchestrator.get_context_for_query(query)
                print(f"  相关表: {context['primary_tables']}")
                print(f"  业务上下文: {context['business_context']}")
            except Exception as e:
                print(f"  查询失败: {e}")
        
        print("\nRAG系统测试完成！6个专家相关表已成功索引")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 确保环境变量已设置
    if not os.getenv("DASHSCOPE_API_KEY"):
        print("请设置环境变量: DASHSCOPE_API_KEY")
    else:
        asyncio.run(test_rag_system())