#!/usr/bin/env python3
"""
异步数据库迁移验证脚本

验证数据库异步化改造的代码结构和语法正确性。
不依赖外部包安装，仅检查代码结构和逻辑。
"""

import sys
import os
import ast
import re

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_syntax(file_path):
    """检查Python文件的语法正确性"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def verify_async_database_structure():
    """验证异步数据库文件结构"""
    print("=== 验证异步数据库文件结构 ===")
    
    required_files = [
        'app/services/database.py',
        'app/services/db_session.py',
        'app/services/query_service.py',
        'requirements.txt'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
            
            # 检查语法
            is_valid, error = check_syntax(file_path)
            if is_valid:
                print(f"  ✓ 语法正确")
            else:
                print(f"  ✗ 语法错误: {error}")
        else:
            print(f"✗ {file_path} 不存在")

def verify_async_dependencies():
    """验证异步数据库依赖"""
    print("\n=== 验证异步数据库依赖 ===")
    
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'aiomysql' in content:
            print("✓ aiomysql依赖已添加")
        else:
            print("✗ 缺少aiomysql依赖")
            
        if 'asyncpg' in content:
            print("✓ asyncpg依赖已添加")
        else:
            print("✗ 缺少asyncpg依赖")
            
    except Exception as e:
        print(f"✗ 检查requirements.txt时出错: {e}")

def verify_database_config():
    """验证数据库配置"""
    print("\n=== 验证数据库配置 ===")
    
    try:
        with open('app/services/database.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'async_db_manager' in content:
            print("✓ 异步数据库管理器已配置")
        else:
            print("✗ 缺少异步数据库管理器")
            
        if 'init_async_db_manager' in content:
            print("✓ 异步数据库管理器初始化正确")
        else:
            print("✗ 缺少异步数据库管理器初始化")
            
        if 'sync_engine' in content and 'SQLDatabase' in content:
            print("✓ LangChain兼容性保持正确")
        else:
            print("✗ LangChain兼容性配置有问题")
            
    except Exception as e:
        print(f"✗ 检查database.py时出错: {e}")

def verify_async_session_manager():
    """验证异步会话管理器"""
    print("\n=== 验证异步会话管理器 ===")
    
    try:
        with open('app/services/db_session.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'AsyncDatabaseManager' in content:
            print("✓ 异步数据库管理器类定义正确")
        else:
            print("✗ 缺少异步数据库管理器类")
            
        if 'create_async_engine' in content:
            print("✓ 异步引擎创建正确")
        else:
            print("✗ 缺少异步引擎创建")
            
        if 'async_sessionmaker' in content:
            print("✓ 异步会话工厂配置正确")
        else:
            print("✗ 缺少异步会话工厂")
            
        if 'execute_query' in content:
            print("✓ 异步查询方法定义正确")
        else:
            print("✗ 缺少异步查询方法")
            
    except Exception as e:
        print(f"✗ 检查db_session.py时出错: {e}")

def verify_async_query_service():
    """验证异步查询服务"""
    print("\n=== 验证异步查询服务 ===")
    
    try:
        with open('app/services/query_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'async def run_sql' in content:
            print("✓ 异步run_sql函数定义正确")
        else:
            print("✗ 缺少异步run_sql函数")
            
        if 'await async_db_manager.execute_query' in content:
            print("✓ 异步数据库调用正确")
        else:
            print("✗ 缺少异步数据库调用")
            
        if 'await run_sql(sql_query)' in content:
            print("✓ get_data_payload中的异步调用正确")
        else:
            print("✗ get_data_payload中缺少异步调用")
            
        # 检查是否移除了executor包装
        if 'run_in_executor' not in content:
            print("✓ 已移除executor包装")
        else:
            print("⚠ 仍存在executor包装，可能需要进一步清理")
            
    except Exception as e:
        print(f"✗ 检查query_service.py时出错: {e}")

def verify_url_conversion():
    """验证数据库URL转换逻辑"""
    print("\n=== 验证数据库URL转换逻辑 ===")
    
    try:
        with open('app/services/db_session.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'mysql+pymysql' in content and 'mysql+aiomysql' in content:
            print("✓ 数据库URL转换逻辑正确")
        else:
            print("✗ 数据库URL转换逻辑有问题")
            
        if 'replace("mysql+pymysql://", "mysql+aiomysql://")' in content:
            print("✓ URL替换逻辑实现正确")
        else:
            print("✗ URL替换逻辑实现有问题")
            
    except Exception as e:
        print(f"✗ 检查URL转换逻辑时出错: {e}")

def verify_connection_pool_config():
    """验证连接池配置"""
    print("\n=== 验证连接池配置 ===")
    
    try:
        with open('app/services/db_session.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        pool_configs = [
            'pool_size=',
            'max_overflow=',
            'pool_pre_ping=',
            'pool_recycle='
        ]
        
        configured_count = 0
        for config in pool_configs:
            if config in content:
                configured_count += 1
                print(f"  ✓ {config.rstrip('=')} 已配置")
            else:
                print(f"  ✗ {config.rstrip('=')} 未配置")
        
        if configured_count == len(pool_configs):
            print("✓ 连接池配置完整")
        else:
            print(f"⚠ 连接池配置不完整 ({configured_count}/{len(pool_configs)})")
            
    except Exception as e:
        print(f"✗ 检查连接池配置时出错: {e}")

def main():
    """主验证函数"""
    print("异步数据库迁移验证开始...\n")
    
    verify_async_database_structure()
    verify_async_dependencies()
    verify_database_config()
    verify_async_session_manager()
    verify_async_query_service()
    verify_url_conversion()
    verify_connection_pool_config()
    
    print("\n=== 验证完成 ===")
    print("如果所有检查项都显示 ✓，说明异步数据库迁移在代码结构层面是成功的。")
    print("要完全验证功能，需要安装aiomysql依赖包并运行实际测试。")
    print("\n安装命令:")
    print("pip install aiomysql asyncpg")

if __name__ == "__main__":
    main()
