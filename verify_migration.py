#!/usr/bin/env python3
"""
迁移验证脚本

验证Flask到FastAPI迁移的代码结构和语法正确性。
不依赖外部包安装，仅检查代码结构。
"""

import sys
import os
import ast
import importlib.util

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_syntax(file_path):
    """检查Python文件的语法正确性"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        return True, None
    except SyntaxError as e:
        return False, str(e)
    except Exception as e:
        return False, str(e)

def verify_file_structure():
    """验证文件结构是否正确"""
    print("=== 验证文件结构 ===")
    
    required_files = [
        'app/__init__.py',
        'app/routes.py', 
        'app/models.py',
        'app/middleware.py',
        'run.py'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
            
            # 检查语法
            is_valid, error = check_syntax(file_path)
            if is_valid:
                print(f"  ✓ 语法正确")
            else:
                print(f"  ✗ 语法错误: {error}")
        else:
            print(f"✗ {file_path} 不存在")

def verify_imports():
    """验证关键导入是否正确"""
    print("\n=== 验证导入结构 ===")
    
    # 检查app/__init__.py的导入
    try:
        with open('app/__init__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from fastapi import FastAPI' in content:
            print("✓ FastAPI导入正确")
        else:
            print("✗ 缺少FastAPI导入")
            
        if 'from .middleware import setup_middleware' in content:
            print("✓ 中间件导入正确")
        else:
            print("✗ 缺少中间件导入")
            
        if 'from .routes import api_router' in content:
            print("✓ 路由导入正确")
        else:
            print("✗ 缺少路由导入")
            
    except Exception as e:
        print(f"✗ 检查app/__init__.py时出错: {e}")
    
    # 检查routes.py的导入
    try:
        with open('app/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from fastapi import APIRouter' in content:
            print("✓ APIRouter导入正确")
        else:
            print("✗ 缺少APIRouter导入")
            
        if 'from fastapi.responses import StreamingResponse' in content:
            print("✓ StreamingResponse导入正确")
        else:
            print("✗ 缺少StreamingResponse导入")
            
    except Exception as e:
        print(f"✗ 检查app/routes.py时出错: {e}")

def verify_route_definitions():
    """验证路由定义是否正确"""
    print("\n=== 验证路由定义 ===")
    
    try:
        with open('app/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '@api_router.post(\'/suggestions\'' in content:
            print("✓ 建议接口路由定义正确")
        else:
            print("✗ 建议接口路由定义有问题")
            
        if '@api_router.post(\'/query\')' in content:
            print("✓ 查询接口路由定义正确")
        else:
            print("✗ 查询接口路由定义有问题")
            
        if 'api_router = APIRouter()' in content:
            print("✓ 路由器创建正确")
        else:
            print("✗ 路由器创建有问题")
            
    except Exception as e:
        print(f"✗ 检查路由定义时出错: {e}")

def verify_app_factory():
    """验证应用工厂函数"""
    print("\n=== 验证应用工厂函数 ===")
    
    try:
        with open('app/__init__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'def create_app() -> FastAPI:' in content:
            print("✓ 应用工厂函数签名正确")
        else:
            print("✗ 应用工厂函数签名有问题")
            
        if 'app = FastAPI(' in content:
            print("✓ FastAPI应用创建正确")
        else:
            print("✗ FastAPI应用创建有问题")
            
        if 'app.include_router(api_router, prefix="/api")' in content:
            print("✓ 路由注册正确")
        else:
            print("✗ 路由注册有问题")
            
    except Exception as e:
        print(f"✗ 检查应用工厂函数时出错: {e}")

def verify_startup_script():
    """验证启动脚本"""
    print("\n=== 验证启动脚本 ===")
    
    try:
        with open('run.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'import uvicorn' in content:
            print("✓ Uvicorn导入正确")
        else:
            print("✗ 缺少Uvicorn导入")
            
        if 'uvicorn.run(' in content:
            print("✓ Uvicorn启动配置正确")
        else:
            print("✗ Uvicorn启动配置有问题")
            
    except Exception as e:
        print(f"✗ 检查启动脚本时出错: {e}")

def main():
    """主验证函数"""
    print("FastAPI迁移验证开始...\n")
    
    verify_file_structure()
    verify_imports()
    verify_route_definitions()
    verify_app_factory()
    verify_startup_script()
    
    print("\n=== 验证完成 ===")
    print("如果所有检查项都显示 ✓，说明迁移在代码结构层面是成功的。")
    print("要完全验证功能，需要安装依赖包并运行实际测试。")

if __name__ == "__main__":
    main()
